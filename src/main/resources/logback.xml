<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender class="ch.qos.logback.core.ConsoleAppender" name="json">
        <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
            <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"/>
            <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
            <appendLineSeparator>true</appendLineSeparator>
        </layout>
    </appender>

    <logger name="mesh.clink.tech" level="debug"/>

    <logger name="org.springframework" level="info">
        <appender-ref ref="json"/>
    </logger>

    <logger name="jsonLogger" level="TRACE">
        <appender-ref ref="json"/>
    </logger>

    <root level="info">
        <appender-ref ref="json"/>
    </root>
</configuration>