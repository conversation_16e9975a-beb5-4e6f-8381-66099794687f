create table portfolio.param_validation_ref
(
    code             int
        constraint param_validation_ref_pk
            primary key,
    kafka_event_code int
        constraint param_validation_kafka_event_code_fkey
            references portfolio.kafka_event_ref,
    param_name       varchar,
    param_value      varchar,
    portfolio_value  varchar,
    is_not_null      boolean
);

ALTER TABLE portfolio.kafka_event_ref
    ADD COLUMN topic varchar;

update portfolio.kafka_event_ref
set topic = 'eks.cmd.portfolio.0'
where code = 1;
update portfolio.kafka_event_ref
set topic = 'portfolio.fct.gratitude-achievements.0'
where code = 2;
insert into portfolio.kafka_event_ref (code, value, topic)
VALUES (3, 'Достижения ЕИС ДОП', 'fudo.fct.additional-education-achievements.0');

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (1, 3, 'achievement.result', 'winner', 'Победитель', false);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (2, 3, 'achievement.result', 'awardee', 'Призер', false);

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (3, 3, 'achievement.contest.level', 'inter-municipal', '4', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (4, 3, 'achievement.contest.level', 'inter-regional', '5', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (5, 3, 'achievement.contest.level', 'international', '6', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (6, 3, 'achievement.contest.level', 'municipal', '2', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (7, 3, 'achievement.contest.level', 'national', '5', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (8, 3, 'achievement.contest.level', 'organizational', '1', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (9, 3, 'achievement.contest.level', 'regional', '4', true);

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (10, 3, 'achievement.contest.type', '1', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (11, 3, 'achievement.contest.type', '2', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (12, 3, 'achievement.contest.type', '3', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (13, 3, 'achievement.contest.type', '4', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (14, 3, 'achievement.contest.type', '5', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (15, 3, 'achievement.contest.type', '6', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (16, 3, 'achievement.contest.type', '7', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (17, 3, 'achievement.contest.type', '8', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (18, 3, 'achievement.contest.type', '9', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (19, 3, 'achievement.contest.type', '10', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (20, 3, 'achievement.contest.type', '11', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (21, 3, 'achievement.contest.type', '12', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (22, 3, 'achievement.contest.type', '13', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (23, 3, 'achievement.contest.type', '14', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (24, 3, 'achievement.contest.type', '15', null, true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (25, 3, 'achievement.contest.type', '16', null, true);

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (26, 3, 'achievement.contest.orientation', '164', '6', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (27, 3, 'achievement.contest.orientation', '165', '2', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (28, 3, 'achievement.contest.orientation', '166', '4', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (29, 3, 'achievement.contest.orientation', '170', '3', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (30, 3, 'achievement.contest.orientation', '172', '3', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (31, 3, 'achievement.contest.orientation', '173', '2', true);

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (32, 3, 'achievement.participationForm', '1', '1', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (33, 3, 'achievement.participationForm', '2', '3', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (34, 3, 'achievement.participationForm', '3', '2', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (35, 3, 'achievement.participationForm', '4', '4', true);

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (36, 3, 'achievement.participationType', 'combined', '1', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (37, 3, 'achievement.participationType', 'individual', '2', true);
insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value, is_not_null)
VALUES (38, 3, 'achievement.participationType', 'team', '3', true);