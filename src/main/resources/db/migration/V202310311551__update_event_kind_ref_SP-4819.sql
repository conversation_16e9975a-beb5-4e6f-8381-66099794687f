INSERT INTO portfolio.event_kind_ref (code, value, category_code, subcategory_code, level_event, organizators) VALUES
(986,'Московский конкурс межпредметных навыков и знаний «Интеллектуальный мегаполис.Потенциал»',8,NULL,4,'ГБОУ ДПО МЦПС'),
(987,'Конкурс детского медиатворчества «День рождения российского кино: 115 лет со дня премьеры первого отечественного игрового фильма»',15,1,NULL,'ГБОУ ДПО ГМЦ '),
(988,'Городской конкурс видеороликов, инфографики и плакатов «Безопасный интернет»',15,1,4,'Г<PERSON><PERSON>У ДПО ГМЦ '),
(989,'ИТ–Марафон',15,21,4,'ГБОУ ДПО ГМЦ '),
(990,'Городской фестиваль Научно–технического творчества молодежи «Образование. Наука. Производство»',15,6,4,'ГБОУ ДПО ГМЦ '),
(991,'Фестиваль «Мир слова»',15,1,4,'ГБОУ ДПО ГМЦ '),
(992,'Фестиваль по аэробике «День аэробики»',28,12,NULL,'ГБОУ ДПО МЦВП'),
(993,'Городской шахматный турнир «Пешка и ферзь»',28,12,4,'ГБОУ ДПО МЦВП'),
(994,'Всероссийские соревнования «Президентские состязания»',28,12,5,'ГБОУ ДПО МЦВП'),
(995,'Всероссийские соревнования «Президентские спортивные игры»',28,12,5,'ГБОУ ДПО МЦВП'),
(996,'Городские соревнования по шашкам «Черные диагонали»',28,12,4,'ГБОУ ДПО МЦВП'),
(997,'Школьная спортивная лига по мини-футболу «Мини-футбол в школу»',28,12,1,'ГБОУ ДПО МЦВП'),
(998,'Всероссийские соревнования Школьной спортивной лиги по футболу',28,12,5,'ГБОУ ДПО МЦВП'),
(999,'Открытый турнир по баскетболу 3х3 среди сборных команд ШСК девушек и команд юношей',28,12,4,'ГБОУ ДПО МЦВП'),
(1000,'Всероссийские соревнования по бадминтону «Проба пера»',28,12,5,'ГБОУ ДПО МЦВП'),
(1001,'Всероссийские соревнования по мини-футболу среди юношей и девушек «Мини-футбол в школу»',28,12,5,'ГБОУ ДПО МЦВП'),
(1002,'Фестивали по чир спорту',28,12,NULL,'ГБОУ ДПО МЦВП'),
(1003,'Всероссийские соревнования школьной спортивной лиги по баскетболу «Победный мяч»',28,12,5,'ГБОУ ДПО МЦВП'),
(1004,'Всероссийские соревнования «Веселые старты» среди обучающихся начальной школы',28,12,5,'ГБОУ ДПО МЦВП'),
(1005,'Всероссийские соревнования школьной спортивной лиги по тэг-регби: «Регбийная школьная лига»',28,12,5,'ГБОУ ДПО МЦВП'),
(1006,'Всероссийские соревнования по волейболу',28,12,5,'ГБОУ ДПО МЦВП'),
(1007,'Выездные мероприятия в рамках Школьного познавательного туризма',28,13,1,'ГБОУДО МДЮЦ ЭКТ'),
(1008,'«КиберПарус 2024»',28,12,NULL,'ГБОУ ДПО МЦПС'),
(1009,'Большой шлюпочный поход',28,13,NULL,'ГБОУ ДПО МЦПС'),
(1010,'Всероссийские соревнования Кубок России по судомодельному спорту в классах NSS-A-B-C-D, F5-moho (MM), F5-mono (RG-65), FF4-A',28,12,5,'ГБОУ ДПО МЦПС'),
(1011,'Открытое Первенство России по стендовому судомоделизму среди юниоров «Москва Златоглавая»',28,12,5,'ГБОУ ДПО МЦПС'),
(1012,'Парусные гонки «Кубок «Патриот.Спорт»',28,12,NULL,'ГБОУ ДПО МЦПС'),
(1013,'Первенство по лазертагу на Кубок Героя России А.В. Днепровского',28,12,NULL,'ГБОУ ДПО МЦПС'),
(1014,'Шахматный турнир «Ладья небесная» на Кубок «Патриот.Спорт»',28,12,NULL,'ГБОУ ДПО МЦПС'),
(1015,'Неделя детской книги',44,18,NULL,'ГБОУ ДПО ГМЦ '),
(1016,'Фестиваль школьных театров «Живая сцена»',44,14,NULL,'ГБОУ ДПО МЦВП'),
(1017,'Проект «Театральное закулисье»',44,14,NULL,'ГБОУ ДПО МЦВП'),
(1018,'Городская командная интеллектуальная игра по краеведению «IQMoscow»',55,10,4,'ГБОУДО МДЮЦ ЭКТ'),
(1019,'Московский городской конкурс исследовательских краеведческих работ "Отечество"',55,10,4,'ГБОУДО МДЮЦ ЭКТ'),
(1020,'Городской экологический фестиваль «Бережём планету вместе». Викторина «Экологический мониторинг окружающей среды»',56,11,4,'ГБОУ ДПО ГМЦ '),
(1021,'Городской экологический фестиваль «Бережём планету вместе». ',56,11,4,'ГБОУ ДПО ГМЦ '),
(1022,'Всероссийский проект «Первая помощь»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1023,'Городской проект «Эти люди –достояние России»',57,8,4,'ГБОУ ДПО ГМЦ '),
(1024,'Волонтерские отряды первых',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1025,'Всероссийский проект «В гостях у ученого»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1026,'Всероссийская программа «Мы - граждане России»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1027,'Конференция регионального отделения Российского движения детей и молодежи "Движение первых" «Поехали!»',57,8,4,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1028,'Всероссийский проект «Хранители истории»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1029,'Акселератор проектов «О!Идея»',57,8,NULL,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1030,'Фестиваль первичных отделений регионального отделения Российского движения детей и молодежи «Движение первых» города Москвы',57,8,4,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1031,'Всероссийский проект «Команда первых»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1032,'Всероссийский проект «Практическая академия»',57,8,5,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1033,'Форум детских инициатив «Будущее за нами! / Тебе решать!»',57,8,NULL,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1034,'Выездная программа для участников Движения Первых города Москвы',57,8,4,'ГБОУ ДПО ДТДМ им.А.П.Гайдара'),
(1035,'Образовательный военно-патриотический проект «Юнфлот»',57,8,NULL,'ГБОУ ДПО МЦПС'),
(1036,'Региональный этап военно-патриотической игры «Зарница»',57,8,4,'ГБОУ ДПО МЦПС'),
(1037,'Региональный этап военно-патриотической игры «Победа»',57,8,4,'ГБОУ ДПО МЦПС'),
(1038,'Соревнования и состязания по направлению «морское дело»',57,8,NULL,'ГБОУ ДПО МЦПС'),
(1039,'Европейский чемпионат по профессиональному мастерству EuroSkills (по стандартам Ворлдскиллс)',70,NULL,6,'ГАОУ ДПО МЦРПО'),
(1040,'Национальный чемпионат сквозных рабочих профессий высокотехнологичных отраслей промышленности "Хайтек"',70,NULL,5,'ГАОУ ДПО МЦРПО'),
(1041,'Отраслевой чемпионат в сфере информационных технологий DigitalSkills',70,NULL,5,'ГАОУ ДПО МЦРПО'),
(1042,'Мировой чемпионат по профессиональному мастерству (по стандартам Ворлдскиллс)',70,NULL,6,'ГАОУ ДПО МЦРПО'),
(1043,'Отборочный этап чемпионата по профессиональному мастерству «Профессионалы»',70,NULL,5,'ГАОУ ДПО МЦРПО'),
(1044,'Национальный открытый чемпионат творческих компетенций "Artmasters"',70,NULL,5,'ГАОУ ДПО МЦРПО');

update portfolio.event_kind_ref SET is_archive = true WHERE code IN (297, 770, 910, 746, 978, 979, 955, 958, 965);

UPDATE portfolio.section_ref SET value = 'Демонстрационный экзамен', short_value = 'демонстр. экзамен' WHERE code = 71;
UPDATE portfolio.section_settings_ref SET value = 'Демонстрационный экзамен' WHERE code IN (42);