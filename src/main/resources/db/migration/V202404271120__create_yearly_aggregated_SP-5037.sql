create table portfolio.yearly_aggregated
(
    id                         bigint primary key,
    creation_date              timestamp not null default now(),
    edit_date                  timestamp not null default now(),
    person_id                  varchar   not null,
    cultural_count             integer,
    achievements_count         integer,
    first_achievement_date     date,
    olympiad_not_rewarded      integer,
    olympiad_rewarded          integer,
    competitions_not_rewarded  integer,
    competitions_rewarded      integer,
    additional_education_count integer,
    first_interest_name        varchar,
    first_interest_popularity  integer,
    second_interest_name       varchar,
    second_interest_popularity integer,
    year                       varchar
);