create table portfolio.yearly_result_completion
(
    id                                bigint primary key,
    is_process_started                bool default false,
    is_olympiad_completed             bool default false,
    is_competition_completed          bool default false,
    is_interests_completed            bool default false,
    is_achievements_completed         bool default false,
    is_additional_education_completed bool default false,
    is_cultural_completed             bool default false,
    is_message_formation_completed    bool default false,
    year                              varchar
);
insert into portfolio.yearly_result_completion (id, is_process_started, is_olympiad_completed, is_competition_completed,
                                                is_interests_completed, is_achievements_completed,
                                                is_additional_education_completed, is_cultural_completed,
                                                is_message_formation_completed, year)
values (nextval('hibernate_sequence'), false, false, false, false, false, false, false, false, '2023-2024');

CREATE OR REPLACE FUNCTION portfolio.gather_yearly_interests()
    RETURNS boolean
AS
$$
BEGIN
    WITH ranked_interest AS (
        with popularity as (select interest_head_code,
                                   category_code,
                                   subcategory_code,
                                   count(distinct person_id) as popularity
                            from portfolio.interest_view
                            group by interest_head_code, category_code, subcategory_code)
        select person_id,
               interest_head_code,
               interest_head_value,
               category_code,
               subcategory_code,
               interest_name,
               popularity,
               rank
        from (select ROW_NUMBER() OVER (PARTITION BY person_id ORDER BY popularity desc) AS rank,
                     person_id,
                     iv.interest_head_code,
                     iv.interest_head_value,
                     iv.category_code,
                     iv.subcategory_code,
                     (CASE
                          WHEN subcategory_value is not null THEN concat(category_value, '(', subcategory_value, ')')
                          ELSE category_value END)                                       as interest_name,
                     popularity.popularity
              from portfolio.interest_view iv
                       left join popularity on iv.interest_head_code = popularity.interest_head_code and
                                               iv.category_code = popularity.category_code and
                                               (iv.subcategory_code is null or
                                                iv.subcategory_code = popularity.subcategory_code)
              group by person_id, iv.interest_head_code, iv.interest_head_value, iv.category_code, category_value,
                       iv.subcategory_code,
                       subcategory_value, popularity.popularity
              order by person_id, popularity desc) as temp
        where temp.rank <= 2
    )
    INSERT
    INTO portfolio.yearly_interest (id, interest_count, person_id, first_interest_name, first_interest_popularity,
                                    second_interest_name,
                                    second_interest_popularity, year)
    SELECT nextval('hibernate_sequence'),
           COUNT(person_id)                                                                  as interest_count,
           person_id,
           MAX(CASE WHEN rank = 1 THEN concat(interest_head_value, ': ', interest_name) END) AS first_interest_name,
           MAX(CASE WHEN rank = 1 THEN popularity END)                                       AS first_interest_popularity,
           MAX(CASE WHEN rank = 2 THEN concat(interest_head_value, ': ', interest_name) END) AS second_interest_name,
           MAX(CASE WHEN rank = 2 THEN popularity END)                                       AS second_interest_popularity,
           '2023-2024'
    FROM ranked_interest
    WHERE ranked_interest.rank <= 2
    group by person_id;
    update portfolio.yearly_result_completion set is_interests_completed = true where "year" = '2023-2024';
    return true;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.gather_yearly_achievements()
    RETURNS boolean
AS
$$
BEGIN
    insert into portfolio.yearly_achievements(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               person_id,
               sum(count)            as achievements_count,
               min(first_earnt_date) as first_achievement_date,
               '2023-2024'
        FROM ((SELECT person_id, count(*) as count, min(date) as first_earnt_date
               FROM portfolio.reward r
               WHERE (r.reward_type_code NOT IN (4, 10, 16, 22, 27, 33, 39) OR
                      (r.reward_type_code IN (4, 10, 16, 22, 27, 33, 39) AND
                       (lower(r.name) = '1 место' or lower(r.name) = '2 место' or lower(r.name) = '3 место')))
                 AND source_code not in (10, 11)
                 AND r.date > '2023-09-01'
                 AND r.is_delete = false
               GROUP BY r.person_id)
              UNION ALL
              (SELECT person_id, count(*) as count, min(date) as first_earnt_date
               FROM portfolio.sport_reward sr
               WHERE (sr.type_code NOT IN (4, 10, 16, 22, 27, 33, 39) OR
                      (sr.type_code IN (4, 10, 16, 22, 27, 33, 39) AND
                       (lower(sr.name) = '1 место' or lower(sr.name) = '2 место' or lower(sr.name) = '3 место')))
                 AND source_code not in (10, 11)
                 AND sr.date > '2023-09-01'
                 AND sr.is_delete = false
               GROUP BY sr.person_id)) as grouped
        group by person_id);
    update portfolio.yearly_result_completion set is_achievements_completed = true where "year" = '2023-2024';
    return true;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.gather_yearly_olympiads()
    RETURNS boolean
AS
$$
BEGIN
    insert into portfolio.yearly_olympiads(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id                               AS person_id,
               COUNT(CASE
                         WHEN (r.entity_id IS NULL AND sr.entity_id IS NULL OR
                               (r.reward_type_code NOT IN
                                (1, 2, 3))
                             OR (sr.type_code NOT IN
                                 (1, 2, 3))) THEN 1 END) AS count_not_rewarded,
               COUNT(CASE
                         WHEN (r.reward_type_code IN
                               (1, 2, 3))
                             OR (sr.type_code IN
                                 (1, 2, 3)) THEN 1 END)  AS count_rewarded,
               '2023-2024'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.date > '2023-09-01'
            AND r.is_delete = false
            AND r.source_code not in (10, 11)
            AND r.reward_type_code IN (1, 2, 3)
                 LEFT JOIN portfolio.sport_reward sr ON e.id = CAST(sr.entity_id as bigint)
            AND sr.date > '2023-09-01'
            AND sr.is_delete = false
            AND sr.source_code not in (10, 11)
            AND sr.type_code IN (1, 2, 3)
        WHERE e.is_delete = false
          AND e.start_date > '2023-09-01'
          AND e.type_code = 8
          AND e.source_code not in (10, 11)
        GROUP BY e.person_id);
    update portfolio.yearly_result_completion set is_olympiad_completed = true where "year" = '2023-2024';
    return true;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.gather_yearly_competitions()
    RETURNS boolean
AS
$$
BEGIN
    insert into portfolio.yearly_competitions(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id                          AS person_id,
               COUNT(CASE
                         WHEN (r.entity_id IS NULL AND sr.entity_id IS NULL OR
                               (r.reward_type_code NOT IN
                                (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                                 29, 30, 31, 32, 34, 35, 36))
                             OR (sr.type_code NOT IN
                                 (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
                                  19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34,
                                  36))) THEN 1 END) AS count_not_rewarded,
               COUNT(CASE
                         WHEN (r.reward_type_code IN
                               (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                                29, 30, 31, 32, 34, 35, 36))
                             OR (sr.type_code IN
                                 (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
                                  19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34,
                                  36)) THEN 1 END)  AS count_rewarded,
               '2023-2024'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.date > '2023-09-01'
            AND r.is_delete = false
            AND r.reward_type_code IN
                (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                 29, 30, 31, 32, 34, 35, 36)
                 LEFT JOIN portfolio.sport_reward sr ON e.id = CAST(sr.entity_id as bigint)
            AND sr.date > '2023-09-01'
            AND sr.is_delete = false
            AND sr.type_code IN
                (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
                 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34,
                 36)
        WHERE e.is_delete = false
          AND e.start_date > '2023-09-01'
          AND e.type_code IN (15, 16, 28, 29, 30, 44, 55, 56, 57, 72)
        GROUP BY e.person_id);
    update portfolio.yearly_result_completion set is_competition_completed = true where "year" = '2023-2024';
    return true;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.gather_yearly_additional_education()
    RETURNS boolean
AS
$$
BEGIN
    insert into portfolio.yearly_additional_education(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               person_id,
               count(*) as additional_education_count,
               '2023-2024'
        FROM portfolio.employment e
        WHERE e.start_date > '2023-09-01'
           OR (e.end_date IS NULL OR e.end_date > '2023-09-01')
            AND e.is_delete = false
        GROUP BY e.person_id);
    update portfolio.yearly_result_completion set is_achievements_completed = true where "year" = '2023-2024';
    return true;
END;
$$ LANGUAGE plpgsql;