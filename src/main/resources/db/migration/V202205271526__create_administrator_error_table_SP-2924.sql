create table portfolio.error_type_ref
(
    code  int
        constraint error_type_ref_pk
            primary key,
    value varchar(250)
);

insert into portfolio.error_type_ref (code, value)
values (1, 'В данных карточки допущена ошибка'),
       (2, 'Указанные данные не относятся ко мне');

create table portfolio.administrator_error_record
(
    id                          bigint
        constraint administrator_error_record_pk
            primary key,
    person_id                   varchar(255) not null,
    creator_id                  varchar(255) not null,
    source_code                 int          not null,
    creation_date               timestamp without time zone,
    entity_id                   bigint,
    entity_type                 varchar(20),
    record_id                   varchar(255),
    error_type_code             int          not null
        constraint fk_administrator_error_record_error_type_ref
            references portfolio.error_type_ref,
    error_general_message       text,
    error_file_metadata_message text,
    error_child_entity_message  text
);

