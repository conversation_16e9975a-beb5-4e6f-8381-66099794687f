CREATE TABLE portfolio.mark_average
(
    id                            bigint    not null
        constraint mark_average_pkey
            primary key,
    person_id                     varchar   not null,
    creation_date                 timestamp not null,
    edit_date                     timestamp not null,
    source_code                   smallint
        constraint mark_average_source_code_fkey
            references portfolio.data_source_ref,
    is_delete                     boolean default false,
    mark_id                       varchar   not null,
    mark_type_code                smallint
        constraint mark_average_mark_type_fkey
            references portfolio.mark_type_ref,
    subject_id                    bigint    not null,
    subject_name                  varchar   not null,
    organization_id               integer   not null,
    class_id                      varchar   not null,
    class_name                    varchar   not null,
    parallel_id                   integer   not null,
    parallel                      varchar   not null,
    grade_system_type_code        smallint
        constraint mark_average_grade_system_fkey
            references portfolio.grade_system_type_ref,
    value                         varchar   not null,
    five_point_value              float,
    year                          varchar   not null,
    theme_id                      integer,
    theme_name                    varchar,
    attestation_period_id         integer,
    attestation_period_begin_date timestamp,
    attestation_period_end_date   timestamp,
    is_border_line                boolean,
    comment                       varchar,
    curator_staff_id              integer
);