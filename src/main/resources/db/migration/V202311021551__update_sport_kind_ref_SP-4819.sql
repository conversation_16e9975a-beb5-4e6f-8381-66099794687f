INSERT INTO portfolio.sport_kind_ref (code, value, code_type_sport, parent_id, is_archive) VALUES
(11366,'Авиационные гонки','0001411Л',NULL,FALSE),
(11367,'класс - летательный аппарат массой до 115 кг с поршневым двигателем','0011811Л',11366,FALSE),
(11368,'класс - летательный аппарат массой до 115 кг с реактивным двигателем','0021811Л',11366,FALSE),
(11369,'класс - самолет поршневой','0031811Л',11366,FALSE),
(11370,'класс - самолет реактивный','0041811Л',11366,FALSE),
(11371,'класс - самолет реактивный - высший пилотаж','0051811Л',11366,FALSE),
(11372,'класс - самолет реактивный - высший пилотаж - известная программа - 1','0061811Л',11366,FALSE),
(11373,'класс - самолет реактивный - высший пилотаж - известная программа - 2','0071811Л',11366,FALSE),
(11374,'двоеборье','0081811Л',11366,FALSE),
(11375,'флаг - футбол','0022811Г',6173,FALSE),
(11376,'Зимнее плавание','0001412Л',NULL,FALSE),
(11377,'баттерфляй 25 м','0011812Л',11376,FALSE),
(11378,'брасс 25 м','0021812Л',11376,FALSE),
(11379,'брасс 50 м','0031812Л',11376,FALSE),
(11380,'брасс 100 м','0041812Л',11376,FALSE),
(11381,'брасс 200 м','0051812Л',11376,FALSE),
(11382,'вольный стиль 25 м','0061812Л',11376,FALSE),
(11383,'вольный стиль 50 м','0071812Л',11376,FALSE),
(11384,'вольный стиль 100 м','0081812Л',11376,FALSE),
(11385,'вольный стиль 200 м','0091812Л',11376,FALSE),
(11386,'эстафета 4 человека х 25 м - брасс','0101812Л',11376,FALSE),
(11387,'эстафета 4 человека х 25 м - брасс - смешанная','0111812Л',11376,FALSE),
(11388,'эстафета 4 человека х 25 м - вольный стиль','0121812Л',11376,FALSE),
(11389,'эстафета 4 человека х 25 м - вольный стиль - смешанная','0131812Л',11376,FALSE),
(11390,'эстафета 4 человека х 25 м - комбинированная - смешанная','0141812Л',11376,FALSE),
(11391,'Киокушин','0001411Я',NULL,FALSE),
(11392,'весовая категория 25 кг','0011811Ю',11391,FALSE),
(11393,'весовая категория 27,5 кг','0021811Ю',11391,FALSE),
(11394,'весовая категория 30 кг','0031811Н',11391,FALSE),
(11395,'весовая категория 32,5 кг','0041811Ю',11391,FALSE),
(11396,'весовая категория 35 кг','0051811Н',11391,FALSE),
(11397,'весовая категория 37,5 кг','0061811Ю',11391,FALSE),
(11398,'весовая категория 40 кг','0071811Н',11391,FALSE),
(11399,'весовая категория 42,5 кг','0081811Ю',11391,FALSE),
(11400,'весовая категория 45 кг','0091811Н',11391,FALSE),
(11401,'весовая категория 45+ кг','0101811Д',11391,FALSE),
(11402,'весовая категория 47,5 кг','0111811Ю',11391,FALSE),
(11403,'весовая категория 50 кг','0121811С',11391,FALSE),
(11404,'весовая категория 50+ кг','0131811Ю',11391,FALSE),
(11405,'весовая категория 52,5 кг','0141811Н',11391,FALSE),
(11406,'весовая категория 55 кг','0151811С',11391,FALSE),
(11407,'весовая категория 55+ кг','0161811Д',11391,FALSE),
(11408,'весовая категория 57,5 кг','0171811Ю',11391,FALSE),
(11409,'весовая категория 60 кг','0181811Я',11391,FALSE),
(11410,'весовая категория 60+ кг','0191811Н',11391,FALSE),
(11411,'весовая категория 65 кг','0201811Я',11391,FALSE),
(11412,'весовая категория 65+ кг','0211811Ю',11391,FALSE),
(11413,'весовая категория 70 кг','0221811Г',11391,FALSE),
(11414,'весовая категория 70+ кг','0231811Э',11391,FALSE),
(11415,'весовая категория 75 кг','0241811А',11391,FALSE),
(11416,'весовая категория 80 кг','0251811А',11391,FALSE),
(11417,'весовая категория 80+ кг','0261811Ю',11391,FALSE),
(11418,'весовая категория 85 кг','0271811М',11391,FALSE),
(11419,'весовая категория 90 кг','0281811М',11391,FALSE),
(11420,'весовая категория 95 кг','0291811М',11391,FALSE),
(11421,'весовая категория 95+ кг','0301811М',11391,FALSE),
(11422,'абсолютная категория','0311811Л',11391,FALSE),
(11423,'ката','0321811Я',11391,FALSE),
(11424,'ката - группа','0331811Я',11391,FALSE),
(11425,'тамэсивари','0341811Л',11391,FALSE),
(11426,'Муайтай','0001511Я',NULL,FALSE),
(11427,'весовая категория 30 кг','0161811Н',11426,FALSE),
(11428,'весовая категория 32 кг','0181811Н',11426,FALSE),
(11429,'весовая категория 34 кг','0201811Н',11426,FALSE),
(11430,'весовая категория 36 кг','0011811Н',11426,FALSE),
(11431,'весовая категория 38 кг','0021811Н',11426,FALSE),
(11432,'весовая категория 40 кг','0031811Н',11426,FALSE),
(11433,'весовая категория 40,91 кг','0291811Ж',11426,FALSE),
(11434,'весовая категория 42 кг','0041811Н',11426,FALSE),
(11435,'весовая категория 43,18 кг','0301811Ж',11426,FALSE),
(11436,'весовая категория 44 кг','0051811Н',11426,FALSE),
(11437,'весовая категория 45 кг','0211811С',11426,FALSE),
(11438,'весовая категория 45,45 кг','0341811Ж',11426,FALSE),
(11439,'весовая категория 46 кг','0061811Н',11426,FALSE),
(11440,'весовая категория 47,62 кг','0351811Л',11426,FALSE),
(11441,'весовая категория 48 кг','0071811Я',11426,FALSE),
(11442,'весовая категория 48,99 кг','0361811Л',11426,FALSE),
(11443,'весовая категория 50 кг','0081811Н',11426,FALSE),
(11444,'весовая категория 50,80 кг','0371811Л',11426,FALSE),
(11445,'весовая категория 51 кг','0091811Я',11426,FALSE),
(11446,'весовая категория 52 кг','0101811Н',11426,FALSE),
(11447,'весовая категория 52,16 кг','0381811Л',11426,FALSE),
(11448,'весовая категория 53,52 кг','0391811Л',11426,FALSE),
(11449,'весовая категория 54 кг','0111811Я',11426,FALSE),
(11450,'весовая категория 55,34 кг','0401811Л',11426,FALSE),
(11451,'весовая категория 56 кг','0121811Н',11426,FALSE),
(11452,'весовая категория 57 кг','0131811Я',11426,FALSE),
(11453,'весовая категория 57,15 кг','0411811Л',11426,FALSE),
(11454,'весовая категория 58 кг','0141811Н',11426,FALSE),
(11455,'весовая категория 58,87 кг','0421811Л',11426,FALSE),
(11456,'весовая категория 60 кг','0151811Я',11426,FALSE),
(11457,'весовая категория 61,24 кг','0431811Л',11426,FALSE),
(11458,'весовая категория 63,5 кг','0171811Я',11426,FALSE),
(11459,'весовая категория 63,50 кг','0441811Л',11426,FALSE),
(11460,'весовая категория 63,5+ кг','0231811Д',11426,FALSE),
(11461,'весовая категория 66,68 кг','0451811Л',11426,FALSE),
(11462,'весовая категория 67 кг','0191811Я',11426,FALSE),
(11463,'весовая категория 69,85 кг','0461811Л',11426,FALSE),
(11464,'весовая категория 71 кг','0221811Я',11426,FALSE),
(11465,'весовая категория 71+ кг','0241811Н',11426,FALSE),
(11466,'весовая категория 72,58 кг','0471811Л',11426,FALSE),
(11467,'весовая категория 75 кг','0251811Я',11426,FALSE),
(11468,'весовая категория 75+ кг','0261811Б',11426,FALSE),
(11469,'весовая категория 76,20 кг','0481811Л',11426,FALSE),
(11470,'весовая категория 79,38 кг','0491811Л',11426,FALSE),
(11471,'весовая категория 79,38+ кг','0501811Ж',11426,FALSE),
(11472,'весовая категория 81 кг','0281811А',11426,FALSE),
(11473,'весовая категория 81+ кг','0271811Ю',11426,FALSE),
(11474,'весовая категория 82,55 кг','0511811М',11426,FALSE),
(11475,'весовая категория 86 кг','0311811А',11426,FALSE),
(11476,'весовая категория 86,18 кг','0521811М',11426,FALSE),
(11477,'весовая категория 90,70 кг','0531811М',11426,FALSE),
(11478,'весовая категория 90,70+ кг','0541811М',11426,FALSE),
(11479,'весовая категория 91 кг','0321811А',11426,FALSE),
(11480,'весовая категория 91+ кг','0331811А',11426,FALSE),
(11481,'про-амат - весовая категория 40,91 кг','0551811Д',11426,FALSE),
(11482,'про-амат - весовая категория 43,18 кг','0561811Д',11426,FALSE),
(11483,'про-амат - весовая категория 45,45 кг','0571811Д',11426,FALSE),
(11484,'про-амат - весовая категория 47,62 кг','0581811Н',11426,FALSE),
(11485,'про-амат - весовая категория 48,99 кг','0591811Н',11426,FALSE),
(11486,'про-амат - весовая категория 50,80 кг','0601811Н',11426,FALSE),
(11487,'про-амат - весовая категория 52,16 кг','0611811Н',11426,FALSE),
(11488,'про-амат - весовая категория 53,52 кг','0621811Н',11426,FALSE),
(11489,'про-амат - весовая категория 55,34 кг','0631811Н',11426,FALSE),
(11490,'про-амат - весовая категория 57,15 кг','0641811Н',11426,FALSE),
(11491,'про-амат - весовая категория 58,87 кг','0651811Н',11426,FALSE),
(11492,'про-амат - весовая категория 61,24 кг','0661811Н',11426,FALSE),
(11493,'про-амат - весовая категория 63,50 кг','0671811Н',11426,FALSE),
(11494,'про-амат - весовая категория 66,68 кг','0681811Н',11426,FALSE),
(11495,'про-амат - весовая категория 69,85 кг','0691811Н',11426,FALSE),
(11496,'про-амат - весовая категория 72,58 кг','0701811Н',11426,FALSE),
(11497,'про-амат - весовая категория 76,20 кг','0711811Н',11426,FALSE),
(11498,'про-амат - весовая категория 79,38 кг','0721811Н',11426,FALSE),
(11499,'про-амат - весовая категория 79,38+ кг','0731811Д',11426,FALSE),
(11500,'про-амат - весовая категория 82,55 кг','0741811Ю',11426,FALSE),
(11501,'про-амат - весовая категория 86,18 кг','0751811Ю',11426,FALSE),
(11502,'про-амат - весовая категория 90,70 кг','0761811Ю',11426,FALSE),
(11503,'про-амат - весовая категория 90,70+ кг','0771811Ю',11426,FALSE),
(11504,'Пилонный спорт','0001411Я',NULL,FALSE),
(11505,'соло','0011811Я',11504,FALSE),
(11506,'двойки','0021811Я',11504,FALSE),
(11507,'двойки - смешанные','0031811Я',11504,FALSE),
(11508,'Роуп скиппинг (спортивная скакалка)','0001411Я',NULL,FALSE),
(11509,'вольные упражнения','0011411Я',11508,FALSE),
(11510,'вольные упражнения - группа','0021411Я',11508,FALSE),
(11511,'прыжки за 30 с','0031411Я',11508,FALSE),
(11512,'прыжки за 180 с','0041411Я',11508,FALSE),
(11513,'прыжки двойные','0051411Я',11508,FALSE),
(11514,'прыжки тройные','0061411Я',11508,FALSE),
(11515,'прыжки 4 человека','0071411Я',11508,FALSE),
(11516,'прыжки через две скакалки 1 человек','0081411Я',11508,FALSE),
(11517,'прыжки через две скакалки 2 человека','0091411Я',11508,FALSE),
(11518,'прыжки через две скакалки 4 человека','0101411Я',11508,FALSE),
(11519,'командные соревнования','0111411Я',11508,FALSE),
(11520,'Спортивное программирование','0001311Я',NULL,FALSE),
(11521,'программирование алгоритмическое','0011811Я',11520,FALSE),
(11522,'программирование продуктовое','0021811Я',11520,FALSE),
(11523,'программирование беспилотных авиационных систем','0031811Я',11520,FALSE),
(11524,'программирование робототехники','0041811Я',11520,FALSE),
(11525,'программирование систем информационной безопасности','0051811Я',11520,FALSE),
(11526,'Фиджитал спорт (функционально-цифровой спорт)','0008311Л',NULL,FALSE),
(11527,'двоеборье - тактическая стрельба','0012811Л',11526,FALSE),
(11528,'ритм - симулятор','0021811Л',11526,FALSE);

UPDATE portfolio.sport_kind_ref SET is_archive = TRUE WHERE code IN (6540,7197,7233,10713) OR parent_id IN (6540,7197,7233,10713);