INSERT INTO portfolio.param_validation_ref (code, kafka_event_code, param_name, param_value, portfolio_value,
                                            is_not_null,
                                            param_type_code)
VALUES (47, 4, 'type', 'create', '1', TRUE, 3),
       (48, 4, 'type', 'update', '2', TRUE, 3),
       (49, 4, 'type', 'delete', '3', TRUE, 3),
       (50, 4, 'participant.PersonId', NULL, NULL, TRUE, 3),
       (51, 4, 'participation.joinDate', NULL, NULL, TRUE, 4),
       (52, 4, 'participation.leaveDate', NULL, NULL, FALSE, 4),
       (53, 4, 'participation.position', NULL, NULL, FALSE, 3),
       (54, 4, 'participation.additionalInfo', NULL, NULL, FALSE, 3),
       (55, 4, 'participation.address', NULL, NULL, FALSE, 3);