UPDATE portfolio.param_validation_ref SET param_name = 'result' WHERE code = 1;
UPDATE portfolio.param_validation_ref SET param_name = 'achievement.result' WHERE code = 2;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 3;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 4;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 5;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 6;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 7;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 8;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.level' WHERE code = 9;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 10;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 11;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 12;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 13;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 14;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 15;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 16;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 17;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 18;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 19;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 20;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 21;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 22;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 23;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 24;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.type' WHERE code = 25;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 26;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 27;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 28;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 29;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 30;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.orientation' WHERE code = 31;
UPDATE portfolio.param_validation_ref SET param_name = 'participationForm' WHERE code = 32;
UPDATE portfolio.param_validation_ref SET param_name = 'participationForm' WHERE code = 33;
UPDATE portfolio.param_validation_ref SET param_name = 'participationForm' WHERE code = 34;
UPDATE portfolio.param_validation_ref SET param_name = 'participationForm' WHERE code = 35;
UPDATE portfolio.param_validation_ref SET param_name = 'participationType' WHERE code = 36;
UPDATE portfolio.param_validation_ref SET param_name = 'participationType' WHERE code = 37;
UPDATE portfolio.param_validation_ref SET param_name = 'participationType' WHERE code = 38;
UPDATE portfolio.param_validation_ref SET param_name = 'achievementId' WHERE code = 39;
UPDATE portfolio.param_validation_ref SET param_name = 'personId' WHERE code = 40;
UPDATE portfolio.param_validation_ref SET param_name = 'dateOfAward' WHERE code = 41;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.name' WHERE code = 42;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.startDate' WHERE code = 43;
UPDATE portfolio.param_validation_ref SET param_name = 'contest.endDate' WHERE code = 44;
UPDATE portfolio.param_validation_ref SET param_name = 'organizationId' WHERE code = 45;
UPDATE portfolio.param_validation_ref SET param_name = 'organizationName' WHERE code = 46;