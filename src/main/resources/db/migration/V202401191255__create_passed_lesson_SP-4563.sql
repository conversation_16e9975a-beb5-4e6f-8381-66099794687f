CREATE TABLE portfolio.passed_lesson
(
    id                    bigint    not null
        constraint passed_lesson_pkey
            primary key,
    creation_date         timestamp not null,
    edit_date             timestamp not null,
    source_code           smallint
        constraint passed_lesson_source_code_fkey
            references portfolio.data_source_ref,
    is_delete             boolean default false,
    staff_id              integer   not null,
    organization_id       integer   not null,
    class_id              varchar   not null,
    class_name            varchar   not null,
    parallel              varchar   not null,
    subject_id            bigint    not null,
    subject_name          varchar   not null,
    lesson_id             integer   not null,
    lesson_name           varchar   not null,
    lesson_theme          varchar   not null,
    lesson_theme_id       integer   not null,
    lesson_date_time      timestamp not null,
    attestation_period_id integer   not null
);