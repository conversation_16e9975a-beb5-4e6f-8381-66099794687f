create table portfolio.participation_type_ref
(
    code       int
        constraint participation_type_ref_pk
            primary key,
    value      varchar,
    is_archive boolean
);


insert into portfolio.participation_type_ref (code, value, is_archive)
VALUES (1, 'Комбинированный', false);
insert into portfolio.participation_type_ref (code, value, is_archive)
VALUES (2, 'Индивидуальное', false);
insert into portfolio.participation_type_ref (code, value, is_archive)
VALUES (3, 'Групповое', false);