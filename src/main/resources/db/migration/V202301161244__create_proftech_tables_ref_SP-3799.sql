create table portfolio.document_ref
(
    code       smallint
        constraint document_ref_pk
            primary key,
    type_code  smallint not null,
    value      varchar  not null,
    is_archive boolean  not null
);

create table portfolio.metaskill_ref
(
    code       int
        constraint metaskill_ref_pk
            primary key,
    value      varchar not null,
    parent_id  int,
    is_archive boolean not null
);

create table portfolio.spo_status_ref
(
    code       smallint
        constraint spo_status_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table if not exists portfolio.document
(
    id                      integer   not null
        constraint spo_document_pk
            primary key,
    person_id               varchar   not null,
    creation_date           timestamp not null,
    edited_date             timestamp,
    is_delete               boolean   not null,
    source_code             smallint  not null,
    hash_code               varchar   not null,
    creator_id              varchar   not null,
    category_code           smallint  not null,
    data_kind               smallint  not null,
    type_code               smallint  not null,
    document_code           integer   not null,
    name                    varchar   not null,
    organization_name       varchar   not null,
    spo_organization_code   integer   not null,
    education_program       varchar,
    hours                   smallint,
    exam_mark_code          varchar,
    profession              varchar,
    rank_code               integer,
    result_date             timestamp,
    doc_number              varchar,
    reg_number              varchar,
    issue_date              timestamp not null,
    issue_place             varchar   not null,
    profession_program_code integer
);

create table portfolio.metaskill
(
    id              int
        constraint metaskill_pk
            primary key,
    creation_date   timestamp not null,
    edited_date     timestamp,
    person_id       varchar   not null,
    metaskills_code integer[] not null
);

create table portfolio.profession_rank_ref
(
    code       smallint
        constraint profession_rank_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.profession_program_ref
(
    code       smallint
        constraint profession_program_ref_pk
            primary key,
    prof_code  varchar not null,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.spo_gia_mark_ref
(
    code       smallint
        constraint spo_gia_mark_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.spo_organization_ref
(
    code       smallint
        constraint spo_organization_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.salary_range_ref
(
    code       smallint
        constraint salary_range_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.level_business_ref
(
    code       smallint
        constraint level_business_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.study_program_base_ref
(
    code       smallint
        constraint study_program_base_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.basic_education_ref
(
    code       smallint
        constraint basic_education_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.employment_doc_type_ref
(
    code       smallint
        constraint employment_doc_type_ref_pk
            primary key,
    value      varchar not null,
    is_archive boolean not null
);

create table portfolio.spo_status
(
    id              int
        constraint spo_status_pk
            primary key,
    creation_date   timestamp not null,
    edited_date     timestamp,
    person_id       varchar   not null,
    status_code smallint
);
