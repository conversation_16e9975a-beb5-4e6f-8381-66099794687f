create table portfolio.organizator_ref
(
    code int
        constraint organizator_ref_pk
            primary key,
    value varchar(255)
);

INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (1, 'ГБОУ ДПО МЦПС');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (2, 'ГБОУ ГМЦ ДОНМ');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (3, 'ГБОУДО МДЮЦ ЭКТ');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (4, 'ГАОУ ДПО ЦПМ');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (5, 'ГБОУДО ДТДиМ имени А.П.Гайдара');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (6, 'ГБОУ ДО ЦРТДЮ "Гермес"');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (7, 'ГАОУ ДПО МЦРПО');
INSERT INTO "portfolio"."organizator_ref" ("code", "value") VALUES (8, 'Департамент культуры');