--section_ref
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (76, 'Среднее профессиональное обучение', 1, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (77, 'Сведения об обучении', 76, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (78, 'Практика', 76, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (79, 'Документы', 76, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (80, 'Cреднее профессиональное обучение', 68, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (81, 'Метанавыки', 80, false);
INSERT INTO portfolio.section_ref (code, value, parent_id, is_archive
)
VALUES (82, 'Трудоустройство', 80, false);

--document_ref
INSERT INTO portfolio.document_ref(code, type_code, value, is_archive
)
VALUES (1, 79, 'Сертификат', false);
INSERT INTO portfolio.document_ref(code, type_code, value, is_archive
)
VALUES (2, 79, 'Свидетельство о профессии рабочего, должности служащего', false);
INSERT INTO portfolio.document_ref(code, type_code, value, is_archive
)
VALUES (3, 79, 'Удостоверение о краткосрочном квалификационном обучении', false);
INSERT INTO portfolio.document_ref(code, type_code, value, is_archive
)
VALUES (4, 79, 'Удостоверение о повышение квалификации', false);
INSERT INTO portfolio.document_ref(code, type_code, value, is_archive
)
VALUES (5, 79, 'Другое', false);

--metaskill_ref
INSERT INTO portfolio.metaskill_ref
VALUES (1, 'Коммуникабельность', null, false);
INSERT INTO portfolio.metaskill_ref
VALUES (2, 'Эмоциональный интеллект', null, false);
INSERT INTO portfolio.metaskill_ref
VALUES (3, 'Эффективность', null, false);
INSERT INTO portfolio.metaskill_ref
VALUES (4, 'Управление карьерой', null, false);
INSERT INTO portfolio.metaskill_ref
VALUES (5, 'Установление контакта', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (6, 'Поддержка диалога', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (7, 'Импровизация в общении', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (8, 'Деловой этикет (очное общение в коллективе)', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (9, 'Деловой этикет (дистанционное общение)', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (10, 'Обратная связь', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (11, 'Поведение в конфликте', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (12, 'Общение с коллегами (работа с возражениями)', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (13, 'Общение с руководителем (язык выгод)', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (14, 'Лидерство', 1, false);
INSERT INTO portfolio.metaskill_ref
VALUES (15, 'Стрессоустойчивость', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (16, 'Работа с самооценкой', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (17, 'Работа с интересом в профессии', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (18, 'Распознавание эмоций', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (19, 'Работа со страхами', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (20, 'Работа в разновозрастной команде', 2, false);
INSERT INTO portfolio.metaskill_ref
VALUES (21, 'Ведение конспектов', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (22, 'Проактивность', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (23, 'Самомотивация', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (24, 'Тайм-менеджмент', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (25, 'Целеполагание (для тайм-менеджмента)', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (26, 'Эффективное сотрудничество', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (27, 'Финансовая грамотность', 3, false);
INSERT INTO portfolio.metaskill_ref
VALUES (28, 'Этапность карьерного развития', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (29, 'Законодательство для трудоустройства, ч.1', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (30, 'Секреты карьерного роста', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (31, 'Деловой визуальный имидж (дресс-код)', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (32, 'Основы карьерного развития', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (33, 'Карьерное целеполагание', 4, false);
INSERT INTO portfolio.metaskill_ref
VALUES (34, 'Портфолио для карьеры', 4, false);

--spo_status_ref
INSERT INTO portfolio.spo_status_ref
VALUES (1, 'Активно ищу работу', false);
INSERT INTO portfolio.spo_status_ref
VALUES (2, 'Трудоустроен', false);
INSERT INTO portfolio.spo_status_ref
VALUES (3, 'Ищу место для практики', false);
INSERT INTO portfolio.spo_status_ref
VALUES (4, 'Практика пройдена', false);
INSERT INTO portfolio.spo_status_ref
VALUES (5, 'Не ищу работу', false);

--profession_rank_ref
INSERT INTO portfolio.profession_rank_ref
VALUES (1, '1 (первый) разряд', false);
INSERT INTO portfolio.profession_rank_ref
VALUES (2, '2 (второй) разряд', false);
INSERT INTO portfolio.profession_rank_ref
VALUES (3, '3 (третий) разряд', false);
INSERT INTO portfolio.profession_rank_ref
VALUES (4, '4 (четвертый) разряд', false);
INSERT INTO portfolio.profession_rank_ref
VALUES (5, '5 (пятый) разряд', false);
INSERT INTO portfolio.profession_rank_ref
VALUES (6, '6 (шестой) разряд', false);

--profession_program_ref
INSERT INTO portfolio.profession_program_ref
VALUES ('1', '05.01.01', 'Гидрометнаблюдатель', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('2', '05.02.01', 'Картография', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('3', '05.02.02', 'Гидрология', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('4', '05.02.03', 'Метеорология', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('5', '07.02.01', 'Архитектура', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('6', '08.01.01', 'Изготовитель арматурных сеток и каркасов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('7', '08.01.02', 'Монтажник трубопроводов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('8', '08.01.04', 'Кровельщик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('9', '08.01.05', 'Мастер столярно-плотничных и паркетных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('10', '08.01.06', 'Мастер сухого строительства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('11', '08.01.07', 'Мастер строительных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('12', '08.01.09', 'Слесарь по строительно-монтажным работам', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('13', '08.01.10', 'Мастер жилищно-коммунального хозяйства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('14', '08.01.14', 'Монтажник санитарно-технических, вентиляционных систем и оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('15', '08.01.16', 'Электромонтажник по сигнализации, централизации и блокировки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('16', '08.01.18', 'Электромонтажник электрических сетей и электрооборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('17', '08.01.21', 'Монтажник электрических подъемников (лифтов)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('18', '08.01.24', 'Мастер столярно-плотнических, паркетных и стекольных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('19', '08.01.25', 'Мастер отделочных строительных и декоративных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('20', '08.01.26', 'Мастер по ремонту и обслуживанию инженерных систем жилищно-коммунального хозяйства',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('21', '08.02.01', 'Строительство и эксплуатация зданий и сооружений', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('22', '08.02.02', 'Строительство и эксплуатация инженерных сооружений', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('23', '08.02.03', 'Производство неметаллических строительных изделий и конструкций', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('24', '08.02.04', 'Водоснабжение и водоотведение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('25', '08.02.05', 'Строительство и эксплуатация автомобильных дорог и аэродромов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('26', '08.02.06', 'Строительство и эксплуатация городских путей сообщения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('27', '08.02.07',
        'Монтаж и эксплуатация внутренних сантехнических устройств, кондиционирования воздуха и вентиляции', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('28', '08.02.08', 'Монтаж и эксплуатация оборудования и систем газоснабжения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('29', '08.02.09', 'Монтаж, наладка и эксплуатация электрооборудования промышленных и гражданских зданий',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('30', '08.02.10', 'Строительство железных дорог, путь и путевое хозяйство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('31', '08.02.11', 'Управление, эксплуатация и обслуживание многоквартирного дома', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('32', '270802.04', 'Трубоклад', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('33', '270802.10', 'Мастер отделочных строительных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('34', '270809.01', 'Машинист машин и оборудования в производстве цемента', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('35', '270809.02', 'Оператор технологического оборудования в производстве стеновых и вяжущих материалов',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('36', '270809.03', 'Изготовитель железобетонных изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('37', '270835.01', 'Мастер путевых машин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('38', '270835.02', 'Бригадир-путеец', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('39', '270839.02', 'Слесарь по изготовлению деталей и узлов технических систем в строительстве', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('40', '270843.03', 'Электромонтажник-наладчик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('41', '270843.05', 'Электромонтажник по силовым сетям и электрооборудованию', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('42', '270843.06', 'Электромонтажник по электрическим машинам', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('43', '09.02.01', 'Компьютерные системы и комплексы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('44', '09.02.02', 'Компьютерные сети', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('45', '09.02.03', 'Программирование в компьютерных системах', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('46', '09.02.04', 'Информационные системы (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('47', '09.02.05', 'Прикладная информатика (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('48', '09.02.06', 'Сетевое и системное администрирование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('49', '09.02.07', 'Информационные системы и программирование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('50', '230103.02', 'Мастер по обработке цифровой информации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('51', '230103.03', 'Наладчик компьютерных сетей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('52', '230103.04', 'Наладчик аппаратного и программного обеспечения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('53', '10.02.01', 'Организация и технология защиты информации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('54', '10.02.02', 'Информационная безопасность телекоммуникационных систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('55', '10.02.03', 'Информационная безопасность автоматизированных систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('56', '10.02.04', 'Обеспечение информационной безопасности телекоммуникационных систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('57', '10.02.05', 'Обеспечение информационной безопасности автоматизированных систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('58', '11.01.05', 'Монтажник связи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('59', '11.02.01', 'Радиоаппаратостроение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('60', '11.02.02', 'Техническое обслуживание и ремонт радиоэлектронной техники (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('61', '11.02.03', 'Эксплуатация оборудования радиосвязи и электрорадионавигации судов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('62', '11.02.04', 'Радиотехнические комплексы и системы управления космических летательных аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('63', '11.02.05', 'Аудиовизуальная техника', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('64', '11.02.06', 'Техническая эксплуатация транспортного радиоэлектронного оборудования (по видам транспорта)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('65', '11.02.07', 'Радиотехнические информационные системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('66', '11.02.08', 'Средства связи с подвижными объектами', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('67', '11.02.09', 'Многоканальные телекоммуникационные системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('68', '11.02.10', 'Радиосвязь, радиовещание и телевидение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('69', '11.02.11', 'Сети связи и системы коммутации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('70', '11.02.12', 'Почтовая связь', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('71', '11.02.13', 'Твердотельная электроника', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('72', '11.02.14', 'Электронные приборы и устройства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('73', '11.02.15', 'Инфокоммуникационные сети и системы связи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('74', '11.02.16', 'Монтаж, техническое обслуживание и ремонт электронных приборов и устройств', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('75', '210109.01', 'Оператор микроэлектронного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('76', '210109.02', 'Оператор оборудования элионных процессов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('77', '210109.03', 'Наладчик технологического оборудования (электронная техника)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('78', '210109.04', 'Сборщик изделий электронной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('79', '210112.01', 'Сборщик приборов вакуумной электроники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('80', '210401.01', 'Радиомеханик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('81', '210401.02', 'Монтажник радиоэлектронной аппаратуры и приборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('82', '210721.01', 'Радиооператор', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('83', '210723.01', 'Монтажник оборудования радио- и телефонной связи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('84', '210723.03', 'Электромонтер оборудования электросвязи и проводного вещания', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('85', '210723.04',
        'Электромонтер по ремонту линейно-кабельных сооружений телефонной связи и проводного вещания', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('86', '210801.01', 'Оператор связи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('87', '12.01.09',
        'Мастер по изготовлению и сборке деталей и узлов оптических и оптико-электронных приборов и систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('88', '12.02.01', 'Авиационные приборы и комплексы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('89', '12.02.02', 'Акустические приборы и системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('90', '12.02.03', 'Радиоэлектронные приборные устройства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('91', '12.02.04', 'Электромеханические приборные устройства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('92', '12.02.05', 'Оптические и оптико-электронные приборы и системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('93', '12.02.06', 'Биотехнические и медицинские аппараты и системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('94', '12.02.07', 'Монтаж, техническое обслуживание и ремонт медицинской техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('95', '12.02.08', 'Протезно-ортопедическая и реабилитационная техника', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('96', '12.02.09', 'Производство и эксплуатация оптических и оптико-электронных приборов и систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('97', '12.02.10', 'Монтаж, техническое обслуживание и ремонт биотехнических и медицинских аппаратов и систем',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('98', '200409.01', 'Наладчик оборудования оптического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('99', '200409.02', 'Оптик-механик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('100', '200409.03', 'Сборщик очков', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('101', '201014.01', 'Электромеханик по ремонту и обслуживанию наркозно-дыхательной аппаратуры', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('102', '201014.02', 'Электромеханик по ремонту и обслуживанию медицинского оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('103', '201014.03', 'Электромеханик по ремонту и обслуживанию медицинских оптических приборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('104', '201014.04', 'Электромеханик по ремонту и обслуживанию электронной медицинской аппаратуры', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('105', '201016.01', 'Механик протезно-ортопедических изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('106', '13.01.01', 'Машинист котлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('107', '13.01.05', 'Электромонтер по техническому обслуживанию электростанций и сетей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('108', '13.01.06', 'Электромонтер-линейщик по монтажу воздушных линий высокого напряжения и контактной сети',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('109', '13.01.07', 'Электромонтер по ремонту электросетей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('110', '13.01.14', 'Электромеханик по лифтам', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('111', '13.02.01', 'Тепловые электрические станции', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('112', '13.02.02', 'Теплоснабжение и теплотехническое оборудование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('113', '13.02.03', 'Электрические станции, сети и системы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('114', '13.02.04', 'Гидроэлектроэнергетические установки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('115', '13.02.05', 'Технология воды, топлива и смазочных материалов на электрических станциях', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('116', '13.02.06', 'Релейная защита и автоматизация электроэнергетических систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('117', '13.02.07', 'Электроснабжение (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('118', '13.02.08', 'Электроизоляционная, кабельная и конденсаторная техника', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('119', '13.02.09', 'Монтаж и эксплуатация линий электропередачи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('120', '13.02.10', 'Электрические машины и аппараты', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('121', '13.02.11',
        'Техническая эксплуатация и обслуживание электрического и электромеханического оборудования (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('122', '140101.02', 'Машинист паровых турбин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('123', '140404.01', 'Электрослесарь по ремонту оборудования электростанций', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('124', '140407.01', 'Слесарь по ремонту оборудования электростанций', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('125', '140429.01', 'Электромеханик по испытанию и ремонту электрооборудования летательных аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('126', '140446.01', 'Сборщик трансформаторов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('127', '140446.02', 'Сборщик электрических машин и аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('128', '140446.03', 'Электромонтер по ремонту и обслуживанию электрооборудования (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('129', '140446.04', 'Сборщик электроизмерительных приборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('130', '140446.05', 'Электромонтажник-схемщик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('131', '14.02.01', 'Атомные электрические станции и установки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('132', '14.02.02', 'Радиационная безопасность', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('133', '14.02.03', 'Технология разделения изотопов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('134', '15.01.05', 'Сварщик (ручной и частично механизированной сварки (наплавки)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('135', '15.01.31', 'Мастер контрольно-измерительных приборов и автоматики', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('136', '15.01.32', 'Оператор станков с программным управлением', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('137', '15.01.33', 'Токарь на станках с числовым программным управлением', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('138', '15.01.34', 'Фрезеровщик на станках с числовым программным управлением', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('139', '15.01.35', 'Мастер слесарных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('140', '15.01.36', 'Дефектоскопист', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('141', '15.02.01', 'Монтаж и техническая эксплуатация промышленного оборудования (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('142', '15.02.02', 'Техническая эксплуатация оборудования для производства электронной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('143', '15.02.03', 'Техническая эксплуатация гидравлических машин, гидроприводов и гидропневмоавтоматики',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('144', '15.02.04', 'Специальные машины и устройства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('145', '15.02.05', 'Техническая эксплуатация оборудования в торговле и общественном питании', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('146', '15.02.06', 'Монтаж и техническая эксплуатация холодильно-компрессорных машин и установок (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('147', '15.02.07', 'Автоматизация технологических процессов и производств (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('148', '15.02.08', 'Технология машиностроения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('149', '15.02.09', 'Аддитивные технологии', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('150', '15.02.10', 'Мехатроника и мобильная робототехника (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('151', '15.02.11', 'Техническая эксплуатация и обслуживание роботизированного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('152', '15.02.12', 'Монтаж, техническое обслуживание и ремонт промышленного оборудования (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('153', '15.02.13', 'Техническое обслуживание и ремонт систем вентиляции и кондиционирования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('154', '15.02.14', 'Оснащение средствами автоматизации технологических процессов и производств (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('155', '15.02.15', 'Технология металлообрабатывающего производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('156', '150701.01', 'Оператор в производстве металлических изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('157', '150707.01', 'Наладчик холодноштамповочного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('158', '150707.02', 'Наладчик кузнечно-прессового оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('159', '150709.01', 'Наладчик сварочного и газоплазморезательного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('160', '150709.03', 'Сварщик на лазерных установках', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('161', '150709.04', 'Сварщик на электронно-лучевых сварочных установках', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('162', '150711.01', 'Наладчик литейного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('163', '151013.01', 'Машинист лесозаготовительных и трелевочных машин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('164', '151013.02', 'Слесарь по ремонту лесозаготовительного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('165', '151019.01', 'Часовщик-ремонтник', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('166', '151019.02', 'Электромонтажник блоков электронно-механических часов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('167', '151022.01', 'Электромеханик по торговому и холодильному оборудованию', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('168', '151022.02', 'Машинист холодильных установок', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('169', '151031.01', 'Наладчик оборудования в бумажном производстве', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('170', '151031.02', 'Наладчик деревообрабатывающего оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('171', '151031.03', 'Монтажник технологического оборудования (по видам оборудования)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('172', '151031.04', 'Наладчик технологического оборудования в производстве строительных материалов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('173', '151901.01', 'Чертежник-конструктор', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('174', '151902.01', 'Наладчик станков и оборудования в механообработке', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('175', '151902.02', 'Наладчик шлифовальных станков', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('176', '151902.03', 'Станочник (металлообработка)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('177', '151902.04', 'Токарь-универсал', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('178', '151902.05', 'Фрезеровщик-универсал', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('179', '151902.06', 'Шлифовщик-универсал', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('180', '151903.01', 'Контролер станочных и слесарных работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('181', '151903.02', 'Слесарь', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('182', '220703.01', 'Наладчик контрольно-измерительных приборов и автоматики', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('183', '220703.02', 'Слесарь по контрольно-измерительным приборам и автоматике', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('184', '220703.03', 'Электромонтер охранно-пожарной сигнализации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('185', '18.01.01', 'Лаборант по физико-механическим испытаниями', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('186', '18.01.33',
        'Лаборант по контролю качества сырья, реактивов, промежуточных продуктов, готовой продукции, отходов производства (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('187', '18.02.01', 'Аналитический контроль качества химических соединений', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('188', '18.02.02', 'Химическая технология отделочного производства и обработки изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('189', '18.02.03', 'Химическая технология неорганических веществ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('190', '18.02.04', 'Электрохимическое производство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('191', '18.02.05', 'Производство тугоплавких неметаллических и силикатных материалов и изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('192', '18.02.06', 'Химическая технология органических веществ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('193', '18.02.07', 'Технология производства и переработки пластических масс и эластомеров', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('194', '18.02.08', 'Технология кинофотоматериалов и магнитных носителей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('195', '18.02.09', 'Переработка нефти и газа', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('196', '18.02.10', 'Коксохимическое производство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('197', '18.02.11', 'Технология пиротехнических составов и изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('198', '18.02.12', 'Технология аналитического контроля химических соединений.', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('199', '18.02.13', 'Технология производства изделий из полимерных композитов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('200', '240100.02', 'Лаборант-эколог', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('201', '240100.03', 'Аппаратчик-оператор экологических установок', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('202', '240101.01', 'Аппаратчик-оператор нефтехимического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('203', '240101.02', 'Машинист технологических насосов и компрессоров', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('204', '240101.03', 'Оператор нефтепереработки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('205', '240101.04', 'Мастер по обслуживанию магистральных трубопроводов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('206', '240103.01', 'Аппаратчик в производстве химических волокон', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('207', '240103.02', 'Оператор в производстве химических волокон', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('208', '240105.01', 'Изготовитель изделий строительной керамики', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('209', '240107.01', 'Аппаратчик-оператор производства неорганических веществ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('210', '240107.02',
        'Оператор производства стекловолокна, стекловолокнистых материалов и изделий стеклопластиков', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('211', '240107.03', 'Аппаратчик производства стекловолокнистых материалов и стеклопластиков', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('212', '240107.04', 'Мастер-изготовитель деталей и изделий из стекла', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('213', '240107.05', 'Мастер-обработчик стекла и стеклоизделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('214', '240107.06', 'Отдельщик и резчик стекла', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('215', '240107.07', 'Контролер стекольного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('216', '240107.08', 'Изготовитель фарфоровых и фаянсовых изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('217', '240107.09', 'Отделочник и комплектовщик фарфоровых и фаянсовых изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('218', '240107.10', 'Контролер-приемщик фарфоровых, фаянсовых и керамических изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('219', '240107.11', 'Изготовитель эмалированной посуды', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('220', '240123.01', 'Аппаратчик производства синтетических смол и пластических масс', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('221', '240123.02', 'Машинист-оператор в производстве изделий из пластмасс', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('222', '240123.03', 'Прессовщик изделий из пластмасс', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('223', '240123.04',
        'Машинист-аппаратчик подготовительных процессов в производстве резиновых смесей, резиновых технических изделий и шин',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('224', '240123.05', 'Оператор в производстве шин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('225', '240123.06', 'Оператор процессов вулканизации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('226', '240123.07', 'Мастер шиномонтажной мастерской', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('227', '240123.08', 'Оператор в производстве резиновых технических изделий и обуви', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('228', '240136.01', 'Аппаратчик-оператор коксохимического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('229', '240136.02', 'Машинист машин коксохимического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('230', '240302.01', 'Аппаратчик-оператор азотных производств и продуктов органического синтеза', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('231', '240700.01', 'Лаборант-аналитик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('232', '240705.01', 'Аппаратчик-оператор в биотехнологии', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('233', '19.02.01', 'Биохимическое производство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('234', '19.02.02', 'Технология хранения и переработки зерна', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('235', '19.02.03', 'Технология хлеба, кондитерских и макаронных изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('236', '19.02.04', 'Технология сахаристых продуктов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('237', '19.02.05', 'Технология бродильных производств и виноделие', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('238', '19.02.06', 'Технология консервов и пищеконцентратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('239', '19.02.07', 'Технология молока и молочных продуктов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('240', '19.02.08', 'Технология мяса и мясных продуктов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('241', '19.02.09', 'Технология жиров и жирозаменителей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('242', '19.02.10', 'Технология продукции общественного питания', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('243', '260101.01', 'Аппаратчик элеваторного, мукомольного, крупяного и комбикормового производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('244', '260103.01', 'Пекарь', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('245', '260103.03', 'Оператор поточно-автоматической линии (макаронное производство)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('246', '260105.01', 'Аппаратчик производства сахара', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('247', '260105.02', 'Кондитер сахаристых изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('248', '260107.01', 'Пивовар', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('249', '260121.01', 'Наладчик оборудования в производстве пищевой продукции (по отраслям производства)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('250', '260201.01', 'Мастер производства молочной продукции', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('251', '260201.02', 'Изготовитель мороженого', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('252', '260203.01', 'Переработчик скота и мяса', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('253', '260203.02', 'Обработчик птицы и кроликов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('254', '260203.03', 'Оператор процессов колбасного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('255', '260207.01', 'Аппаратчик получения растительного масла', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('256', '260207.02', 'Оператор линии производства маргарина', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('257', '20.02.01', 'Рациональное использование природохозяйственных комплексов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('258', '20.02.02', 'Защита в чрезвычайных ситуациях', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('259', '20.02.03', 'Природоохранное обустройство территорий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('260', '20.02.04', 'Пожарная безопасность', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('261', '20.02.05', 'Организация оперативного (экстренного) реагирования в чрезвычайных ситуациях', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('262', '280705.01', 'Пожарный', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('263', '21.02.01', 'Разработка и эксплуатация нефтяных и газовых месторождений', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('264', '21.02.02', 'Бурение нефтяных и газовых скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('265', '21.02.03', 'Сооружение и эксплуатация газонефтепроводов и газонефтехранилищ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('266', '21.02.04', 'Землеустройство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('267', '21.02.05', 'Земельно-имущественные отношения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('268', '21.02.06', 'Информационные системы обеспечения градостроительной деятельности', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('269', '21.02.07', 'Аэрофотогеодезия', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('270', '21.02.08', 'Прикладная геодезия', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('271', '21.02.09', 'Гидрогеология и инженерная геология', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('272', '21.02.10', 'Геология и разведка нефтяных и газовых месторождений', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('273', '21.02.11', 'Геофизические методы поисков и разведки месторождений полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('274', '21.02.12', 'Технология и техника разведки месторождений полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('275', '21.02.13', 'Геологическая съемка, поиски и разведка месторождений полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('276', '21.02.14', 'Маркшейдерское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('277', '21.02.15', 'Открытые горные работы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('278', '21.02.16', 'Шахтное строительство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('279', '21.02.17', 'Подземная разработка месторождений полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('280', '21.02.18', 'Обогащение полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('281', '130401.01', 'Ремонтник горного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('282', '130404.01', 'Машинист на открытых горных работах', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('283', '130404.02', 'Машинист машин по добыче и переработке торфа', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('284', '130405.01', 'Горнорабочий на подземных работах', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('285', '130405.02', 'Машинист электровоза (на горных выработках)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('286', '130405.03', 'Проходчик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('287', '130405.04', 'Горномонтажник подземный', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('288', '130405.05', 'Электрослесарь подземный', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('289', '130406.01', 'Обогатитель полезных ископаемых', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('290', '131003.01', 'Оператор нефтяных и газовых скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('291', '131003.02', 'Оператор по ремонту скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('292', '131003.03', 'Бурильщик эксплуатационных и разведочных скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('293', '131003.04', 'Машинист на буровых установках', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('294', '131003.05', 'Оператор (моторист) по цементажу скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('295', '131003.06', 'Вышкомонтажник (широкого профиля)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('296', '131012.01', 'Бурильщик морского бурения скважин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('297', '22.02.01', 'Металлургия черных металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('298', '22.02.02', 'Металлургия цветных металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('299', '22.02.03', 'Литейное производство черных и цветных металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('300', '22.02.04', 'Металловедение и термическая обработка металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('301', '22.02.05', 'Обработка металлов давлением', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('302', '22.02.06', 'Сварочное производство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('303', '22.02.07', 'Порошковая металлургия, композиционные материалы, покрытия', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('304', '150400.01', 'Машинист крана металлургического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('305', '150400.02', 'Контролер металлургического производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('306', '150401.01', 'Доменщик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('307', '150401.02', 'Сталеплавильщик (по типам производства)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('308', '150402.01', 'Аппаратчик-оператор в производстве цветных металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('309', '150402.02', 'Оператор-обработчик цветных металлов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('310', '150406.01', 'Модельщик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('311', '150412.01', 'Оператор прокатного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('312', '150413.01', 'Оператор трубного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('313', '150421.01', 'Оператор в производстве огнеупоров', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('314', '23.01.17', 'Мастер по ремонту и обслуживанию автомобилей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('315', '23.02.01', 'Организация перевозок и управление на транспорте (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('316', '23.02.02', 'Автомобиле- и тракторостроение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('317', '23.02.03', 'Техническое обслуживание и ремонт автомобильного транспорта', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('318', '23.02.04',
        'Техническая эксплуатация подъемно-транспортных, строительных, дорожных машин и оборудования (по отраслям)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('319', '23.02.05',
        'Эксплуатация транспортного электрооборудования и автоматики (по видам транспорта, за исключением водного)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('320', '23.02.06', 'Техническая эксплуатация подвижного состава железных дорог', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('321', '23.02.07', 'Техническое обслуживание и ремонт двигателей, систем и агрегатов автомобилей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('322', '190623.01', 'Машинист локомотива', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('323', '190623.03', 'Слесарь по обслуживанию и ремонту подвижного состава', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('324', '190623.04',
        'Слесарь-электрик по ремонту электрооборудования подвижного состава (электровозов, электропоездов)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('325', '190623.05', 'Слесарь-электрик метрополитена', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('326', '190627.01', 'Водитель городского электротранспорта', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('327', '190627.02', 'Слесарь по ремонту городского электротранспорта', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('328', '190629.01', 'Машинист дорожных и строительных машин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('329', '190629.07', 'Машинист крана (крановщик)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('330', '190629.08', 'Слесарь по ремонту строительных машин', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('331', '190631.01', 'Автомеханик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('332', '190700.01', 'Оператор транспортного терминала', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('333', '190700.02', 'Докер-механизатор', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('334', '190901.01', 'Электромонтер тяговой подстанции', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('335', '190901.02', 'Электромонтер устройств сигнализации, централизации, блокировки (СЦБ)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('336', '190901.03', 'Оператор поста централизации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('337', '190901.04', 'Составитель поездов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('338', '24.02.01', 'Производство летательных аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('339', '24.02.02', 'Производство авиационных двигателей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('340', '24.02.03', 'Испытание летательных аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('341', '160108.01', 'Слесарь-механик авиационных приборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('342', '160108.02', 'Слесарь-сборщик авиационной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('343', '160108.03', 'Слесарь по ремонту авиационной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('344', '160108.04', 'Электромонтажник авиационной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('345', '25.02.01', 'Техническая эксплуатация летательных аппаратов и двигателей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('346', '25.02.02', 'Обслуживание летательных аппаратов горюче-смазочными материалами', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('347', '25.02.03', 'Техническая эксплуатация электрифицированных и пилотажно-навигационных комплексов',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('348', '25.02.04', 'Летная эксплуатация летательных аппаратов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('349', '25.02.05', 'Управление движением воздушного транспорта', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('350', '25.02.06', 'Производство и обслуживание авиационной техники', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('351', '25.02.07', 'Техническое обслуживание авиационных двигателей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('352', '25.02.08', 'Эксплуатация беспилотных авиационных систем', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('353', '26.01.05', 'Электрорадиомонтажник судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('354', '26.02.01', 'Эксплуатация внутренних водных путей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('355', '26.02.02', 'Судостроение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('356', '26.02.03', 'Судовождение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('357', '26.02.04', 'Монтаж и техническое обслуживание судовых машин и механизмов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('358', '26.02.05', 'Эксплуатация судовых энергетических установок', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('359', '26.02.06', 'Эксплуатация судового электрооборудования и средств автоматики', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('360', '180103.01', 'Судостроитель-судоремонтник металлических судов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('361', '180103.02', 'Судостроитель-судоремонтник неметаллических судов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('362', '180103.03', 'Слесарь-монтажник судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('363', '180103.04', 'Слесарь-механик судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('364', '180107.01', 'Моторист (машинист)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('365', '180107.02', 'Механик маломерного судна', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('366', '180107.03', 'Машинист-котельный судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('367', '180107.04', 'Электрик судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('368', '180107.05', 'Моторист судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('369', '180403.01', 'Судоводитель-помощник механика маломерного судна', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('370', '180403.02', 'Матрос', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('371', '180403.03', 'Водолаз', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('372', '27.02.01', 'Метрология', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('373', '27.02.02', 'Техническое регулирование и управление качеством', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('374', '27.02.03', 'Автоматика и телемеханика на транспорте (железнодорожном транспорте)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('375', '27.02.04', 'Автоматические системы управления', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('376', '27.02.05', 'Системы и средства диспетчерского управления', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('377', '27.02.06', 'Контроль работы измерительных приборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('378', '27.02.07', 'Управление качеством продукции, процессов и услуг (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('379', '29.01.05', 'Закройщик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('380', '29.01.10', 'Модистка головных уборов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('381', '29.02.01', 'Конструирование, моделирование и технология изделий из кожи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('382', '29.02.02', 'Технология кожи и меха', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('383', '29.02.03', 'Конструирование, моделирование и технология изделий из меха', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('384', '29.02.04', 'Конструирование, моделирование и технология швейных изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('385', '29.02.05', 'Технология текстильных изделий (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('386', '29.02.06', 'Полиграфическое производство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('387', '29.02.07', 'Производство изделий из бумаги и картона', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('388', '29.02.08', 'Технология обработки алмазов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('389', '29.02.09', 'Печатное дело.', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('390', '261103.01', 'Контролер качества текстильных изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('391', '261103.02', 'Оператор крутильного оборудования (для всех видов производств)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('392', '261103.03', 'Оператор оборудования чесального производства (для всех видов производств)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('393', '261103.04', 'Оператор прядильного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('394', '261103.05', 'Раклист', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('395', '261103.06', 'Ткач', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('396', '261103.07', 'Оператор вязально-швейного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('397', '261103.08', 'Вязальщица текстильно-галантерейных изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('398', '261103.09', 'Оператор производства нетканых материалов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('399', '261103.10', 'Красильщик (общие профессии производства текстиля)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('400', '261103.11', 'Оператор оборудования отделочного производства (общие профессии производства текстиля)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('401', '261103.12', 'Аппаратчик отделочного производства (общие профессии производства текстиля)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('402', '261401.01', 'Огранщик алмазов в бриллианты', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('403', '261701.01', 'Наладчик полиграфического оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('404', '261701.02', 'Оператор электронного набора и верстки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('405', '261701.03', 'Переплетчик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('406', '261701.04', 'Печатник плоской печати', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('407', '261701.05', 'Мастер печатного дела', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('408', '262005.01', 'Обувщик (широкого профиля)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('409', '262005.02', 'Сборщик обуви', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('410', '262005.03', 'Раскройщик материалов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('411', '262005.04', 'Скорняк', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('412', '262019.01', 'Художник по костюму', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('413', '#VALUE!', '#VALUE!', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('414', '262019.04', 'Оператор швейного оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('415', '262019.05', 'Вышивальщица', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('416', '262023.01', 'Мастер столярного и мебельного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('417', '262023.02', 'Обойщик мебели', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('418', '31.02.01', 'Лечебное дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('419', '31.02.02', 'Акушерское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('420', '31.02.03', 'Лабораторная диагностика', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('421', '31.02.04', 'Медицинская оптика', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('422', '31.02.05', 'Стоматология ортопедическая', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('423', '31.02.06', 'Стоматология профилактическая', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('424', '32.02.01', 'Медико-профилактическое дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('425', '33.02.01', 'Фармация', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('426', '34.02.01', 'Сестринское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('427', '34.02.02', 'Медицинский массаж (для обучения лиц с ограниченными возможностями здоровья по зрению)',
        'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('428', '060501.01', 'Младшая медицинская сестра по уходу за больными', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('429', '35.02.01', 'Лесное и лесопарковое хозяйство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('430', '35.02.02', 'Технология лесозаготовок', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('431', '35.02.03', 'Технология деревообработки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('432', '35.02.04', 'Технология комплексной переработки древесины', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('433', '35.02.05', 'Агрономия', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('434', '35.02.06', 'Технология производства и переработки сельскохозяйственной продукции', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('435', '35.02.07', 'Механизация сельского хозяйства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('436', '35.02.08', 'Электрификация и автоматизация сельского хозяйства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('437', '35.02.09', 'Ихтиология и рыбоводство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('438', '35.02.10', 'Обработка водных биоресурсов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('439', '35.02.11', 'Промышленное рыболовство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('440', '35.02.12', 'Садово-парковое и ландшафтное строительство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('441', '35.02.13', 'Пчеловодство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('442', '35.02.14', 'Охотоведение и звероводство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('443', '35.02.15', 'Кинология', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('444', '35.02.16', 'Эксплуатация и ремонт сельскохозяйственной техники и оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('445', '110401.01', 'Мастер растениеводства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('446', '110401.02', 'Овощевод защищенного грунта', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('447', '110800.01', 'Мастер сельскохозяйственного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('448', '110800.02', 'Тракторист-машинист сельскохозяйственного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('449', '110800.03',
        'Электромонтер по ремонту и обслуживанию электрооборудования в сельскохозяйственном производстве', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('450', '110800.04', 'Мастер по техническому обслуживанию и ремонту машинно-тракторного парка', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('451', '110800.05', 'Заготовитель продуктов и сырья', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('452', '112201.01', 'Хозяйка(ин) усадьбы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('453', '112201.02', 'Управляющий сельской усадьбой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('454', '250101.01', 'Мастер по лесному хозяйству', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('455', '250109.01', 'Мастер садово-паркового и ландшафтного строительства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('456', '250401.01', 'Оператор линий и установок в деревообработке', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('457', '250401.02', 'Станочник-обработчик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('458', '250401.03', 'Станочник деревообрабатывающих станков', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('459', '250401.06', 'Контролер полуфабрикатов и изделий из древесины', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('460', '250401.07', 'Машинист машин по производству бумаги и картона', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('461', '250401.08', 'Сушильщик в бумажном производстве', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('462', '250401.09', 'Контролер целлюлозно-бумажного производства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('463', '36.02.01', 'Ветеринария', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('464', '36.02.02', 'Зоотехния', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('465', '111101.01', 'Мастер животноводства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('466', '111101.02', 'Тренер-наездник лошадей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('467', '111201.01', 'Пчеловод', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('468', '111401.01', 'Рыбовод', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('469', '111402.01', 'Обработчик рыбы и морепродуктов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('470', '111501.01', 'Рыбак прибрежного лова', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('471', '111601.01', 'Оленевод-механизатор', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('472', '111601.02', 'Охотник промысловый', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('473', '111801.01', 'Младший ветеринарный фельдшер', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('474', '38.02.01', 'Экономика и бухгалтерский учет (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('475', '38.02.02', 'Страховое дело (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('476', '38.02.03', 'Операционная деятельность в логистике', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('477', '38.02.04', 'Коммерция (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('478', '38.02.05', 'Товароведение и экспертиза качества потребительских товаров', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('479', '38.02.06', 'Финансы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('480', '38.02.07', 'Банковское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('481', '080110.02', 'Контролер банка', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('482', '080203.01', 'Оператор диспетчерской (производственно-диспетчерской) службы', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('483', '100701.01', 'Продавец, контролер-кассир', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('484', '39.02.01', 'Социальная работа', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('485', '39.02.02', 'Организация сурдокоммуникации', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('486', '040401.01', 'Социальный работник', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('487', '40.02.01', 'Право и организация социального обеспечения', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('488', '40.02.02', 'Правоохранительная деятельность', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('489', '40.02.03', 'Право и судебное администрирование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('490', '42.02.01', 'Реклама', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('491', '42.02.02', 'Издательское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('492', '031601.01', 'Агент рекламный', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('493', '43.01.09', 'Повар, кондитер', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('494', '43.02.01', 'Организация обслуживания в общественном питании', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('495', '43.02.02', 'Парикмахерское искусство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('496', '43.02.03', 'Стилистика и искусство визажа', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('497', '43.02.04', 'Прикладная эстетика', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('498', '43.02.05', 'Флористика', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('499', '43.02.06', 'Сервис на транспорте (по видам транспорта)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('500', '43.02.07', 'Сервис по химической обработке изделий', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('501', '43.02.08', 'Сервис домашнего и коммунального хозяйства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('502', '43.02.09', 'Ритуальный сервис', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('503', '43.02.10', 'Туризм', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('504', '43.02.11', 'Гостиничный сервис', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('505', '43.02.12', 'Технология эстетических услуг', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('506', '43.02.13', 'Технология парикмахерского искусства', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('507', '43.02.14', 'Гостиничное дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('508', '43.02.15', 'Поварское и кондитерское дело', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('509', '100107.01', 'Слесарь по эксплуатации и ремонту газового оборудования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('510', '100114.01', 'Официант, бармен', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('511', '100115.01', 'Аппаратчик химической чистки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('512', '100116.01', 'Парикмахер', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('513', '100120.01', 'Бортпроводник судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('514', '100120.02', 'Повар судовой', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('515', '100120.03', 'Оператор по обработке перевозочных документов на железнодорожном транспорте', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('516', '100120.04', 'Проводник на железнодорожном транспорте', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('517', '44.02.01', 'Дошкольное образование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('518', '44.02.02', 'Преподавание в начальных классах', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('519', '44.02.03', 'Педагогика дополнительного образования', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('520', '44.02.04', 'Специальное дошкольное образование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('521', '44.02.05', 'Коррекционная педагогика в начальном образовании', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('522', '44.02.06', 'Профессиональное обучение (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('523', '46.02.01', 'Документационное обеспечение управления и архивоведение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('524', '034700.01', 'Секретарь', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('525', '034700.02', 'Архивариус', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('526', '034700.03', 'Делопроизводитель', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('527', '49.02.01', 'Физическая культура', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('528', '49.02.02', 'Адаптивная физическая культура', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('529', '49.02.03', 'Спорт', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('530', '50.02.01', 'Мировая художественная культура', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('531', '51.02.01', 'Народное художественное творчество (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('532', '51.02.02', 'Социально-культурная деятельность (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('533', '51.02.03', 'Библиотековедение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('534', '52.02.01', 'Искусство балета', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('535', '52.02.02', 'Искусство танца (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('536', '52.02.03', 'Цирковое искусство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('537', '52.02.04', 'Актерское искусство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('538', '52.02.05', 'Искусство эстрады', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('539', '53.01.01', 'Мастер по ремонту и обслуживанию музыкальных инструментов (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('540', '53.02.01', 'Музыкальное образование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('541', '53.02.02', 'Музыкальное искусство эстрады (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('542', '53.02.03', 'Инструментальное исполнительство (по видам инструментов)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('543', '53.02.04', 'Вокальное искусство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('544', '53.02.05', 'Сольное и хоровое народное пение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('545', '53.02.06', 'Хоровое дирижирование', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('546', '53.02.07', 'Теория музыки', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('547', '53.02.08', 'Музыкальное звукооператорское мастерство', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('548', '53.02.09', 'Театрально-декорационное искусство (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('549', '54.01.20', 'Графический дизайнер', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('550', '54.02.01', 'Дизайн (по отраслям)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('551', '54.02.02', 'Декоративно-прикладное искусство и народные промыслы (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('552', '54.02.03', 'Художественное оформление изделий текстильной и легкой промышленности', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('553', '54.02.04', 'Реставрация', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('554', '54.02.05', 'Живопись (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('555', '54.02.06', 'Изобразительное искусство и черчение', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('556', '54.02.07', 'Скульптура', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('557', '54.02.08', 'Техника и искусство фотографии', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('558', '072200.01', 'Лепщик-модельщик архитектурных деталей', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('559', '072200.02', 'Реставратор строительный', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('560', '072200.03', 'Реставратор тканей, гобеленов и ковров', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('561', '072200.04', 'Реставратор памятников каменного и деревянного зодчества', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('562', '072500.01', 'Исполнитель художественно-оформительских работ', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('563', '072500.02', 'Ювелир', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('564', '072601.01', 'Мастер народных художественных промыслов', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('565', '072601.02', 'Изготовитель художественных изделий из тканей с художественной росписью', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('566', '072602.01', 'Изготовитель художественных изделий из металла', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('567', '072603.01', 'Изготовитель художественных изделий из керамики', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('568', '072608.01', 'Художник декоративной росписи по металлу', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('569', '072608.02', 'Художник росписи по эмали', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('570', '072608.03', 'Художник росписи по дереву', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('571', '072608.04', 'Художник росписи по ткани', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('572', '072609.01', 'Художник миниатюрной живописи', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('573', '072611.01', 'Изготовитель художественных изделий из дерева', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('574', '072612.01', 'Резчик', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('575', '072614.01', 'Инкрустатор', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('576', '100118.01', 'Фотограф', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('577', '55.02.01', 'Театральная и аудиовизуальная техника (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('578', '55.02.02', 'Анимация (по видам)', 'FALSE');
INSERT INTO portfolio.profession_program_ref
VALUES ('579', '100102.01', 'Киномеханик', 'FALSE');

--spo_organization_ref
INSERT INTO portfolio.spo_organization_ref
VALUES ('1', 'ГБПОУ "Политехнический колледж им. Н.Н.Годовикова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('2', 'ГБПОУ "Западный комплекс непрерывного образования"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('3', 'ГБПОУ "Политехнический колледж № 50 имени дважды героя социалистического труда Н.А.Злобина"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('4', 'ГБПОУ "Политехнический колледж № 8 имени дважды героя Советского союза И.Ф.Павлова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('5', 'ГБПОУ "Политехнический колледж имени П.А.Овчинникова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('6', 'ГБПОУ "Политехнический техникум № 47 имени В.Г.Федорова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('7', 'ГБПОУ "Колледж предпринимательства № 11"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('8', 'ГБПОУ "Московский технологический колледж"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('9', 'ГБПОУ "Образовательный комплекс градостроительства "Столица"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('10', 'ГБПОУ "Колледж архитектуры и строительства № 7"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('11', 'ГБПОУ "Колледж современных технологий имени героя Советского Союза М.Ф.Панова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('12', 'ГБПОУ "Колледж архитектуры, дизайна и реинжиниринга № 26"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('13', 'ГБПОУ "Московский колледж архитектуры и градостроительства"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('14', 'ГБПОУ "Московский образовательный комплекс имени Виктора Талалихина"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('15', 'ГБПОУ "Колледж сферы услуг № 3"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('16', 'ГБПОУ "Колледж сферы услуг № 10"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('17', 'ГБПОУ "Колледж сферы услуг № 32"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('18', 'ГБПОУ "Экономико-технологический колледж № 22"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('19', 'ГБПОУ "Пищевой колледж № 33"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('20', 'ГБПОУ "Столичный колледж индустрии сервиса и гостеприимства"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('21', 'ГБПОУ "Московский образовательный комплекс Запад"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('22', 'ГБПОУ "Образовательный комплекс "Юго-Запад"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('23', 'ГБПОУ "Политехнический техникум № 2"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('24', 'ГБПОУ "Московский колледж управление, гостиничного бизнеса и информационных технологий "Царицыно"',
        'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('25', 'ГБПОУ "Московский государственный колледж электромеханики и информационных технологий"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('26', 'ГБПОУ "Колледж автоматизации и информационных технологий № 20"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('27', 'ГБПОУ "Колледж индустрии, гостеприимства и менеджмента № 23"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('28', 'ГБПОУ "Колледж связи № 54 имени П.М.Вострухина"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('29', 'ГБПОУ "Колледж декоративно-прикладного искусства имени Карла Фоберже"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('30', 'ГБПОУ "Московский техникум креативных индустрий им. Л.Б.Красина"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('31', 'ГБПОУ "Технологический колледж № 24"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('32', 'ГБПОУ "Первый московский образовательный комплекс"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('33', 'ГБПОУ "Московский издательско-полиграфический колледж имени Ивана Федорова"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('34', 'ГБПОУ "Колледж малого бизнеса № 4"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('35', 'ГБПОУ "Технологический колледж № 34"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('36', 'ГБПОУ "Колледж железнодорожного и наземного транспорта"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('37', 'ГБПОУ "Колледж автомобильного транспорта № 9"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('38', 'ГБПОУ "Технологический колледж № 21"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('39', 'ГБПОУ "Московский автомобильно-дорожный колледж им. А.А.Николаева"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('40', 'ГБПОУ "Московский индустриальный колледж"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('41', 'ГБОПУ "Технический пожарно-спасательный колледж имени героя РФ В.М.Максимчука"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('42', 'ГБПОУ "Колледж полиции"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('43', 'ГБПОУ "Юридический колледж"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('44', 'ГБПОУ "Финансовый колледж № 35"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('45', 'ГБПОУ "Московский государственный образовательный комплекс"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('46', 'ГБПОУ "Педагогический колледж № 10"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('47', 'ГБПОУ "Киноколледж № 40 "Московская международная киношкола"',
        'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('48', 'ГБПОУ "Педагогический колледж № 18 Митино"', 'FALSE');
INSERT INTO portfolio.spo_organization_ref
VALUES ('49', 'ГБПОУ "Московский колледж бизнес-технологий"', 'FALSE');

--spo_gia_mark_ref
INSERT INTO portfolio.spo_gia_mark_ref (code, value, is_archive)
VALUES ('1', 'отлично', 'FALSE');
INSERT INTO portfolio.spo_gia_mark_ref (code, value, is_archive)
VALUES ('2', 'хорошо', 'FALSE');
INSERT INTO portfolio.spo_gia_mark_ref (code, value, is_archive)
VALUES ('3', 'удовлетворительно (зачтено)', 'FALSE');
INSERT INTO portfolio.spo_gia_mark_ref (code, value, is_archive)
VALUES ('4', 'неудовлетворительно (не зачтено)', 'FALSE');

--section_settings_ref
INSERT INTO portfolio.section_settings_ref
VALUES (45, 'Сведения об обучении', 1);
INSERT INTO portfolio.section_settings_ref
VALUES (46, 'Практика', 1);
INSERT INTO portfolio.section_settings_ref
VALUES (47, 'Документы ', 1);
INSERT INTO portfolio.section_settings_ref
VALUES (48, 'Трудоустройство', 38);
INSERT INTO portfolio.section_settings_ref
VALUES (49, 'Метанавыки', 38);

--data_source_ref
INSERT INTO portfolio.data_source_ref (code, value
)
VALUES ('16', 'АИС "Зачисление в Профтех"');

--salary_range_ref
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('1', '< 10 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('2', '10 000 - 15 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('3', '15 000 - 20 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('4', '20 000 - 25 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('5', '25 000 - 30 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('6', '30 000 - 40 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('7', '>40 000', 'FALSE');
INSERT INTO portfolio.salary_range_ref (code, value, is_archive
)
VALUES ('8', '-', 'FALSE');

--level_business_ref
INSERT INTO portfolio.level_business_ref (code, value, is_archive
)
VALUES ('1', 'М  ≤  100', 'FALSE');
INSERT INTO portfolio.level_business_ref (code, value, is_archive
)
VALUES ('2', '100  <С≤250', 'FALSE');
INSERT INTO portfolio.level_business_ref (code, value, is_archive
)
VALUES ('3', 'К>250', 'FALSE');
INSERT INTO portfolio.level_business_ref (code, value, is_archive
)
VALUES ('4', '-', 'FALSE');

--study_program_base_ref
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('1', 'Без образования', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('2', 'Начальное общее образование (11 классов)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('3', 'Начальное общее образование (12 классов)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('4', 'Начальное общее образование (4 класс)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('5', 'Начальное общее образование (9 классов)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('6', 'Начальное профессиональное образование', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('7', 'Основное общее образование (5-9 класс)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('8', 'Среднее общее образование (10-11 класс)', 'FALSE');
INSERT INTO portfolio.study_program_base_ref (code, value, is_archive
)
VALUES ('9', 'Среднее профессиональное образование', 'FALSE');

--basic_education_ref
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('1', 'Без образования', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('2', 'Высшее профессиональное образование', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('3', 'Начальное общее образование', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('4', 'Начальное профессиональное образование', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('5', 'Основное общее образование', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('6', 'Среднее общее образование', 'FALSE');
INSERT INTO portfolio.basic_education_ref (code, value, is_archive
)
VALUES ('7', 'Среднее профессиональное образование', 'FALSE');

--employment_doc_type_ref
INSERT INTO portfolio.employment_doc_type_ref (code, value, is_archive
)
VALUES ('1', 'Двусторонний', 'FALSE');
INSERT INTO portfolio.employment_doc_type_ref (code, value, is_archive
)
VALUES ('2', 'Трехсторонний', 'FALSE');