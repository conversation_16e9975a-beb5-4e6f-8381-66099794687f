create table if not exists portfolio.yearly_marks
(
    id               bigint primary key default nextval('portfolio.hibernate_sequence'),
    creation_date    timestamp not null default now(),
    edit_date        timestamp not null default now(),
    person_id        varchar   not null,
    message_variant  integer,
    good_amount      integer,
    excellent_amount integer,
    year             varchar            default '2023-2024'
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_marks()
    RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             marks_message_variant, marks_good_amount, marks_excellent_amount, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           message_variant,
           good_amount,
           excellent_amount,
           '2023-2024'
    FROM portfolio.yearly_marks
    ON CONFLICT (person_id, "year") DO UPDATE
        SET marks_message_variant  = EXCLUDED.marks_message_variant,
            marks_good_amount      = EXCLUDED.marks_good_amount,
            marks_excellent_amount = EXCLUDED.marks_excellent_amount;
    RETURN true;
END;
$$ LANGUAGE plpgsql;
