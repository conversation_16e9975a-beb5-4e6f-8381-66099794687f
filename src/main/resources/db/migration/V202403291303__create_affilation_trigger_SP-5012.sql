create table portfolio.api_event_ref
(
    code int primary key,
    value varchar,
    url varchar
);

insert into portfolio.api_event_ref(code, value, url) values (1, 'Движение первых (отправка)', 'portal-api.mos.ru/participants/events/manual/bulk');

create table portfolio.api_writer_log
(
    id bigint
        constraint api_writer_log_pk
            primary key,
    person_id varchar(255),
    creation_date timestamp without time zone not null default now(),
    modification_date timestamp without time zone,
    api_event_code int
        constraint api_writer_log_api_event_code_fk
            references portfolio.api_event_ref,
    status_code int,
    retry_count int,
    entity_id bigint,
    entity_type varchar(255),
    message_data jsonb,
    error_message text
);


CREATE OR REPLACE FUNCTION create_api_writer_record()
    RETURNS TRIGGER AS $$
DECLARE
    action_type TEXT;
BEGIN
    IF TG_OP = 'INSERT' THEN
        action_type := 'create';
    ELSIF TG_OP = 'UPDATE' THEN
        IF NEW.is_delete = false THEN
            action_type := 'update';
        ELSE
            action_type := 'delete';
        END IF;
    END IF;

    INSERT INTO portfolio.api_writer_log (id, creation_date, person_id, api_event_code, status_code, retry_count, entity_id, entity_type, message_data)
    VALUES (nextval('hibernate_sequence'), now(), NEW.person_id, 1, 1, 0, NEW.id, 'Affilation',
            json_build_object(
                    'id', NEW.id,
                    'type', action_type,
                    'participant', json_build_object(
                            'person_id', NEW.person_id
                        ),
                    'participation', json_build_object(
                            'join_date', NEW.start_date,
                            'leave_date', NEW.end_date,
                            'position', NEW.status,
                            'additional_info', NEW.description,
                            'address', NEW.address
                        )
                )
           );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER affiliation_trigger
    AFTER INSERT OR UPDATE ON portfolio.affilation
    FOR EACH ROW
EXECUTE FUNCTION create_api_writer_record();

