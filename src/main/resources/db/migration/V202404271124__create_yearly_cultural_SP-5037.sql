create table portfolio.yearly_cultural
(
    id             bigint primary key,
    creation_date  timestamp not null default now(),
    edit_date      timestamp not null default now(),
    person_id      varchar   not null,
    cultural_count integer,
    year           varchar
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_cultural()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.person_id IN (SELECT person_id FROM portfolio.yearly_aggregated) THEN
        UPDATE portfolio.yearly_aggregated
        SET cultural_count = NEW.cultural_count
        WHERE person_id = NEW.person_id;
    ELSE
        INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, cultural_count, year)
        VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.cultural_count, '2023-2024');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cultural_trigger
    AFTER INSERT
    ON portfolio.yearly_cultural
    FOR EACH ROW
EXECUTE FUNCTION portfolio.update_aggregated_cultural();
