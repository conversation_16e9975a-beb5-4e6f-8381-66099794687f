create table portfolio.administrator_settings_function
(
    id bigint
        constraint administrator_settings_function_pk
            primary key,
    permission_code smallint not null,
    administrator_user_id varchar(50) not null,
    modification_date timestamp not null,
    function_type varchar(50) not null
);

create table portfolio.administrator_settings_function_log
(
    id bigint
        constraint administrator_settings_function_log_pk
            primary key,
    action_type varchar(50) not null,
    administrator_user_id varchar(50) not null,
    creation_date timestamp not null,
    function_type varchar(50) not null
);

INSERT INTO "portfolio"."administrator_settings_function"
("id", "permission_code", "administrator_user_id", "modification_date", "function_type")
VALUES (1, 1, '123', '2021-12-23 00:00:00.000000', 'Search')
