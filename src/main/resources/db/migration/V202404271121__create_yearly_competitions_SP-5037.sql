create table portfolio.yearly_competitions
(
    id                 bigint primary key,
    creation_date      timestamp not null default now(),
    edit_date          timestamp not null default now(),
    person_id          varchar   not null,
    count_not_rewarded integer,
    count_rewarded     integer,
    year               varchar
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_competition()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.person_id IN (SELECT person_id FROM portfolio.yearly_aggregated) THEN
        UPDATE portfolio.yearly_aggregated
        SET competitions_not_rewarded = NEW.count_not_rewarded,
            competitions_rewarded     = NEW.count_rewarded
        WHERE person_id = NEW.person_id;
    ELSE
        INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, competitions_not_rewarded,
                                                 competitions_rewarded, "year")
        VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.count_not_rewarded,
                NEW.count_rewarded, '2023-2024');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER competition_trigger
    AFTER INSERT
    ON portfolio.yearly_competitions
    FOR EACH ROW
EXECUTE FUNCTION portfolio.update_aggregated_competition();