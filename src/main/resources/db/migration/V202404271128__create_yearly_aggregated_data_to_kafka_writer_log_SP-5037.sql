CREATE OR REPLACE FUNCTION portfolio.yearly_aggregated_data_to_kafka_writer_log()
    RETURNS TRIGGER AS
$$
DECLARE
    rec       record;
    json_data json;
BEGIN
    IF NEW.is_olympiad_completed = true AND NEW.is_competition_completed = true AND
       NEW.is_interests_completed = true AND NEW.is_achievements_completed = true AND
       NEW.is_additional_education_completed = true AND NEW.is_cultural_completed = true
        AND NEW.is_message_formation_completed = false THEN
        FOR rec IN SELECT * FROM portfolio.yearly_aggregated WHERE "year" = NEW."year"
            LOOP
                json_data = json_build_object(
                        'personId', rec.person_id,
                        'studentPersonId', rec.person_id,
                        'personRole', 1,
                        'messageData', json_build_object(
                                'culture', json_build_object(
                                        'messageVariant',
                                        CASE
                                            WHEN rec.cultural_count is not null and rec.cultural_count > 5 THEN 1
                                            WHEN rec.cultural_count is not null and rec.cultural_count >= 1 and
                                                 rec.cultural_count <= 5 THEN 2
                                            ELSE 3
                                            END,
                                        'count', coalesce(rec.cultural_count, 0)
                                    ),
                                'olympiads',
                                CASE
                                    WHEN rec.olympiad_not_rewarded is not null OR rec.olympiad_rewarded is not null THEN
                                        json_build_object(
                                                'messageVariant',
                                                CASE
                                                    WHEN rec.olympiad_rewarded is not null AND rec.olympiad_rewarded > 0
                                                        THEN 2
                                                    WHEN rec.olympiad_not_rewarded is not null AND rec.olympiad_not_rewarded > 0
                                                        THEN 1
                                                    END,
                                                'participations', coalesce(rec.olympiad_not_rewarded, 0),
                                                'won', coalesce(rec.olympiad_rewarded, 0)
                                            )
                                    ELSE null
                                    END,
                                'competitions',
                                CASE
                                    WHEN rec.competitions_rewarded is not null OR
                                         rec.competitions_not_rewarded is not null THEN
                                        json_build_object(
                                                'messageVariant',
                                                CASE
                                                    WHEN rec.competitions_rewarded is not null AND
                                                         rec.competitions_rewarded > 0 THEN 2
                                                    WHEN rec.competitions_not_rewarded is not null AND
                                                         rec.competitions_not_rewarded > 0 THEN 1
                                                    END,
                                                'participations', coalesce(rec.competitions_not_rewarded, 0),
                                                'won', coalesce(rec.competitions_rewarded, 0)
                                            )
                                    ELSE null
                                    END,
                                'achievements',
                                CASE
                                    WHEN rec.achievements_count is not null THEN
                                        json_build_object(
                                                'count', rec.achievements_count,
                                                'firstEarntDate', rec.first_achievement_date
                                            )
                                    ELSE null
                                    END,
                                'interests', json_build_object(
                                        'messageVariant',
                                        CASE
                                            WHEN rec.first_interest_name IS NOT NULL AND
                                                 rec.second_interest_name IS NOT NULL THEN 1
                                            WHEN rec.first_interest_name IS NOT NULL THEN 2
                                            ELSE 3
                                            END,
                                        'entities',
                                        CASE
                                            WHEN rec.first_interest_name is not null THEN
                                                json_build_array(
                                                        CASE
                                                            WHEN rec.first_interest_name IS NOT NULL AND
                                                                 rec.second_interest_name IS NOT NULL THEN
                                                                json_build_array(json_build_object(
                                                                                         'interestName',
                                                                                         rec.first_interest_name,
                                                                                         'interestVolume',
                                                                                         rec.first_interest_popularity
                                                                                     ),
                                                                                 json_build_object(
                                                                                         'interestName',
                                                                                         rec.second_interest_name,
                                                                                         'interestVolume',
                                                                                         rec.second_interest_popularity
                                                                                     ))
                                                            WHEN rec.first_interest_name IS NOT NULL THEN
                                                                json_build_array(json_build_object(
                                                                        'interestName', rec.first_interest_name,
                                                                        'interestVolume', rec.first_interest_popularity
                                                                    ))
                                                            END
                                                    )
                                            ELSE json_build_array()
                                            END
                                    ),
                                'additionalEducation', json_build_object(
                                        'messageVariant',
                                        CASE
                                            WHEN rec.additional_education_count IS NOT NULL AND
                                                 rec.additional_education_count > 2 THEN 1
                                            WHEN rec.additional_education_count IS NOT NULL AND
                                                 rec.additional_education_count >= 1 AND
                                                 rec.additional_education_count <= 2 THEN 2
                                            ELSE 3
                                            END,
                                        'count', coalesce(rec.additional_education_count, 0)
                                    )
                            )
                    );

                INSERT INTO portfolio.kafka_writer_log (id, creation_date, kafka_event_code, status_code,
                                                        entity_id, entity_type, error_message, message_data,
                                                        modification_date, topic_name, retry_count)
                VALUES (nextval('hibernate_sequence'), now(), 9, 1, rec.id, 'YearlyResult', null, json_data, now(),
                        'education.fct.person-year-summary.0', 0);

            END LOOP;
        update portfolio.yearly_result_completion
        set is_message_formation_completed = true
        where "year" = '2023-2024';
    END IF;

    RETURN NEW;
END;
$$
    LANGUAGE plpgsql;

-- Создание триггера
CREATE TRIGGER results_update_trigger
    AFTER UPDATE
    ON portfolio.yearly_result_completion
    FOR EACH ROW
EXECUTE FUNCTION portfolio.yearly_aggregated_data_to_kafka_writer_log();