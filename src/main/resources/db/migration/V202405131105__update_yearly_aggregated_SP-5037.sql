ALTER TABLE portfolio.yearly_aggregated
    ADD COLUMN marks_message_variant integer;
ALTER TABLE portfolio.yearly_aggregated
    ADD COLUMN marks_good_amount integer;
ALTER TABLE portfolio.yearly_aggregated
    ADD COLUMN marks_excellent_amount integer;

ALTER TABLE portfolio.yearly_aggregated
    ADD COLUMN success_subject varchar;
ALTER TABLE portfolio.yearly_aggregated
    ADD COLUMN success_subject_mark varchar;

ALTER TABLE portfolio.yearly_aggregated
    ADD UNIQUE (person_id, "year");

ALTER TABLE portfolio.yearly_result_completion
    ADD COLUMN is_marks_completed boolean;
ALTER TABLE portfolio.yearly_result_completion
    ADD COLUMN is_success_subject_completed boolean;

update portfolio.yearly_result_completion
set is_marks_completed           = false,
    is_success_subject_completed = false
where "year" = '2023-2024';