CREATE OR REPLACE FUNCTION portfolio.update_aggregated_interest()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, first_interest_name,
                                             first_interest_popularity, second_interest_name,
                                             second_interest_popularity, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id,
            NEW.first_interest_name, NEW.first_interest_popularity,
            NEW.second_interest_name, NEW.second_interest_popularity, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET first_interest_name        = EXCLUDED.first_interest_name,
            first_interest_popularity  = EXCLUDED.first_interest_popularity,
            second_interest_name       = EXCLUDED.second_interest_name,
            second_interest_popularity = EXCLUDED.second_interest_popularity;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_achievement()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, achievements_count,
                                             first_achievement_date, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id,
            NEW.achievements_count, NEW.first_achievement_date, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET achievements_count     = EXCLUDED.achievements_count,
            first_achievement_date = EXCLUDED.first_achievement_date;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_cultural()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, cultural_count, year)
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.cultural_count, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET cultural_count = EXCLUDED.cultural_count;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_olympiad()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, olympiad_not_rewarded,
                                             olympiad_rewarded, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.count_not_rewarded,
            NEW.count_rewarded, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET olympiad_not_rewarded = EXCLUDED.olympiad_not_rewarded,
            olympiad_rewarded     = EXCLUDED.olympiad_rewarded;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION portfolio.update_aggregated_additional_education()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, additional_education_count,
                                             "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.additional_education_count, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET additional_education_count = EXCLUDED.additional_education_count;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_competition()
    RETURNS TRIGGER AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, competitions_not_rewarded,
                                             competitions_rewarded, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.count_not_rewarded,
            NEW.count_rewarded, '2023-2024')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET competitions_not_rewarded = EXCLUDED.competitions_not_rewarded,
            competitions_rewarded     = EXCLUDED.competitions_rewarded;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;