UPDATE portfolio.avatar_type_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.document SET is_import = FALSE WHERE is_import IS NULL;
UPDATE portfolio.event_kind_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.excel_cache_partition SET saved = FALSE WHERE saved IS NULL;
UPDATE portfolio.favorite_university SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.gia_exam SET approbation = FALSE WHERE approbation IS NULL;
UPDATE portfolio.gia_exam SET is_credit = FALSE WHERE is_credit IS NULL;
UPDATE portfolio.gia_exam SET is_deleted = FALSE WHERE is_deleted IS NULL;
UPDATE portfolio.grade_system_type_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.interest_action_recommend SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.job SET is_by_profile = FALSE WHERE is_by_profile IS NULL;
UPDATE portfolio.job SET is_contract = FALSE WHERE is_contract IS NULL;
UPDATE portfolio.job SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.job SET is_import = FALSE WHERE is_import IS NULL;
UPDATE portfolio.learner_olympiad SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.mark_average SET is_border_line = FALSE WHERE is_border_line IS NULL;
UPDATE portfolio.mark_average SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.mark_final SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.museum_ref SET moskvenok = FALSE WHERE moskvenok IS NULL;
UPDATE portfolio.nsi_organization SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.olympiad_format_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.olympiad_level_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.organizator_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.param_validation_ref SET is_not_null = FALSE WHERE is_not_null IS NULL;
UPDATE portfolio.participation_type_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.passed_lesson SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.person_achievement SET employeecanedit = FALSE WHERE employeecanedit IS NULL;
UPDATE portfolio.person_achievement SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.person_id SET should_update_from_mesh = FALSE WHERE should_update_from_mesh IS NULL;
UPDATE portfolio.proff_classes_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.reward_kind_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.school SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.school_proff_classes_link SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.share_link SET documents = FALSE WHERE documents IS NULL;
UPDATE portfolio.share_link SET interests = FALSE WHERE interests IS NULL;
UPDATE portfolio.share_link SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.share_link SET job = FALSE WHERE job IS NULL;
UPDATE portfolio.share_link SET metaskills = FALSE WHERE metaskills IS NULL;
UPDATE portfolio.share_link SET personal_diagnostic = FALSE WHERE personal_diagnostic IS NULL;
UPDATE portfolio.share_link SET practice = FALSE WHERE practice IS NULL;
UPDATE portfolio.share_link SET profession = FALSE WHERE profession IS NULL;
UPDATE portfolio.share_link SET profession_career_guidance = FALSE WHERE profession_career_guidance IS NULL;
UPDATE portfolio.share_link SET profession_education = FALSE WHERE profession_education IS NULL;
UPDATE portfolio.share_link SET profession_events = FALSE WHERE profession_events IS NULL;
UPDATE portfolio.share_link SET profession_prof_events = FALSE WHERE profession_prof_events IS NULL;
UPDATE portfolio.share_link SET profession_prof_tests = FALSE WHERE profession_prof_tests IS NULL;
UPDATE portfolio.share_link SET profession_recommendations = FALSE WHERE profession_recommendations IS NULL;
UPDATE portfolio.share_link SET profession_rewards = FALSE WHERE profession_rewards IS NULL;
UPDATE portfolio.share_link SET profession_worldskills = FALSE WHERE profession_worldskills IS NULL;
UPDATE portfolio.share_link SET profile = FALSE WHERE profile IS NULL;
UPDATE portfolio.share_link SET training_info = FALSE WHERE training_info IS NULL;
UPDATE portfolio.skipped_lesson SET is_delete = FALSE WHERE is_delete IS NULL;
UPDATE portfolio.specialities_by_industry SET is_archived = FALSE WHERE is_archived IS NULL;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.sport_kind_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.sport_reward_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.subjects_ref SET adaptability = FALSE WHERE adaptability IS NULL;
UPDATE portfolio.subjects_ref SET is_archive = FALSE WHERE is_archive IS NULL;
UPDATE portfolio.yearly_aggregated SET is_sent = FALSE WHERE is_sent IS NULL;
UPDATE portfolio.yearly_aggregated SET not_one = FALSE WHERE not_one IS NULL;
UPDATE portfolio.yearly_result_completion SET is_achievements_completed = FALSE WHERE is_achievements_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_additional_education_completed = FALSE WHERE is_additional_education_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_buy_completed = FALSE WHERE is_buy_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_competition_completed = FALSE WHERE is_competition_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_cultural_completed = FALSE WHERE is_cultural_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_homework_completed = FALSE WHERE is_homework_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_interests_completed = FALSE WHERE is_interests_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_lesson_completed = FALSE WHERE is_lesson_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_marks_completed = FALSE WHERE is_marks_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_message_formation_completed = FALSE WHERE is_message_formation_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_olympiad_completed = FALSE WHERE is_olympiad_completed IS NULL;
UPDATE portfolio.yearly_result_completion SET is_process_started = FALSE WHERE is_process_started IS NULL;
UPDATE portfolio.yearly_result_completion SET is_success_subject_completed = FALSE WHERE is_success_subject_completed IS NULL;
UPDATE portfolio.yearly_success_subject SET not_one = FALSE WHERE not_one IS NULL;


ALTER TABLE portfolio.avatar_type_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.document ALTER COLUMN is_import SET NOT NULL, ALTER COLUMN is_import SET DEFAULT FALSE;
ALTER TABLE portfolio.event_kind_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.excel_cache_partition ALTER COLUMN saved SET NOT NULL, ALTER COLUMN saved SET DEFAULT FALSE;
ALTER TABLE portfolio.favorite_university ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.gia_exam ALTER COLUMN approbation SET NOT NULL, ALTER COLUMN approbation SET DEFAULT FALSE;
ALTER TABLE portfolio.gia_exam ALTER COLUMN is_credit SET NOT NULL, ALTER COLUMN is_credit SET DEFAULT FALSE;
ALTER TABLE portfolio.gia_exam ALTER COLUMN is_deleted SET NOT NULL, ALTER COLUMN is_deleted SET DEFAULT FALSE;
ALTER TABLE portfolio.grade_system_type_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.interest_action_recommend ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.job ALTER COLUMN is_by_profile SET NOT NULL, ALTER COLUMN is_by_profile SET DEFAULT FALSE;
ALTER TABLE portfolio.job ALTER COLUMN is_contract SET NOT NULL, ALTER COLUMN is_contract SET DEFAULT FALSE;
ALTER TABLE portfolio.job ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.job ALTER COLUMN is_import SET NOT NULL, ALTER COLUMN is_import SET DEFAULT FALSE;
ALTER TABLE portfolio.learner_olympiad ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.mark_average ALTER COLUMN is_border_line SET NOT NULL, ALTER COLUMN is_border_line SET DEFAULT FALSE;
ALTER TABLE portfolio.mark_average ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.mark_final ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.museum_ref ALTER COLUMN moskvenok SET NOT NULL, ALTER COLUMN moskvenok SET DEFAULT FALSE;
ALTER TABLE portfolio.nsi_organization ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.olympiad_format_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.olympiad_level_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.organizator_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.param_validation_ref ALTER COLUMN is_not_null SET NOT NULL, ALTER COLUMN is_not_null SET DEFAULT FALSE;
ALTER TABLE portfolio.participation_type_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.passed_lesson ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.person_achievement ALTER COLUMN employeecanedit SET NOT NULL, ALTER COLUMN employeecanedit SET DEFAULT FALSE;
ALTER TABLE portfolio.person_achievement ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.person_id ALTER COLUMN should_update_from_mesh SET NOT NULL, ALTER COLUMN should_update_from_mesh SET DEFAULT FALSE;
ALTER TABLE portfolio.proff_classes_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.reward_kind_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.school ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.school_proff_classes_link ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN documents SET NOT NULL, ALTER COLUMN documents SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN interests SET NOT NULL, ALTER COLUMN interests SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN job SET NOT NULL, ALTER COLUMN job SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN metaskills SET NOT NULL, ALTER COLUMN metaskills SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN personal_diagnostic SET NOT NULL, ALTER COLUMN personal_diagnostic SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN practice SET NOT NULL, ALTER COLUMN practice SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession SET NOT NULL, ALTER COLUMN profession SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_career_guidance SET NOT NULL, ALTER COLUMN profession_career_guidance SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_education SET NOT NULL, ALTER COLUMN profession_education SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_events SET NOT NULL, ALTER COLUMN profession_events SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_prof_events SET NOT NULL, ALTER COLUMN profession_prof_events SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_prof_tests SET NOT NULL, ALTER COLUMN profession_prof_tests SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_recommendations SET NOT NULL, ALTER COLUMN profession_recommendations SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_rewards SET NOT NULL, ALTER COLUMN profession_rewards SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profession_worldskills SET NOT NULL, ALTER COLUMN profession_worldskills SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN profile SET NOT NULL, ALTER COLUMN profile SET DEFAULT FALSE;
ALTER TABLE portfolio.share_link ALTER COLUMN training_info SET NOT NULL, ALTER COLUMN training_info SET DEFAULT FALSE;
ALTER TABLE portfolio.skipped_lesson ALTER COLUMN is_delete SET NOT NULL, ALTER COLUMN is_delete SET DEFAULT FALSE;
ALTER TABLE portfolio.specialities_by_industry ALTER COLUMN is_archived SET NOT NULL, ALTER COLUMN is_archived SET DEFAULT FALSE;
ALTER TABLE portfolio.sport_age_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.sport_kind_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.sport_reward_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.subjects_ref ALTER COLUMN adaptability SET NOT NULL, ALTER COLUMN adaptability SET DEFAULT FALSE;
ALTER TABLE portfolio.subjects_ref ALTER COLUMN is_archive SET NOT NULL, ALTER COLUMN is_archive SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_aggregated ALTER COLUMN is_sent SET NOT NULL, ALTER COLUMN is_sent SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_aggregated ALTER COLUMN not_one SET NOT NULL, ALTER COLUMN not_one SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_achievements_completed SET NOT NULL, ALTER COLUMN is_achievements_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_additional_education_completed SET NOT NULL, ALTER COLUMN is_additional_education_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_buy_completed SET NOT NULL, ALTER COLUMN is_buy_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_competition_completed SET NOT NULL, ALTER COLUMN is_competition_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_cultural_completed SET NOT NULL, ALTER COLUMN is_cultural_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_homework_completed SET NOT NULL, ALTER COLUMN is_homework_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_interests_completed SET NOT NULL, ALTER COLUMN is_interests_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_lesson_completed SET NOT NULL, ALTER COLUMN is_lesson_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_marks_completed SET NOT NULL, ALTER COLUMN is_marks_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_message_formation_completed SET NOT NULL, ALTER COLUMN is_message_formation_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_olympiad_completed SET NOT NULL, ALTER COLUMN is_olympiad_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_process_started SET NOT NULL, ALTER COLUMN is_process_started SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_result_completion ALTER COLUMN is_success_subject_completed SET NOT NULL, ALTER COLUMN is_success_subject_completed SET DEFAULT FALSE;
ALTER TABLE portfolio.yearly_success_subject ALTER COLUMN not_one SET NOT NULL, ALTER COLUMN not_one SET DEFAULT FALSE;