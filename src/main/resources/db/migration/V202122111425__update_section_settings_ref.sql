insert into portfolio.section_settings_ref
values ('1', 'Учеба', null);
insert into portfolio.section_settings_ref
values ('2', 'Наука', null);
insert into portfolio.section_settings_ref
values ('3', 'Спорт', null);
insert into portfolio.section_settings_ref
values ('4', 'Творчество', null);
insert into portfolio.section_settings_ref
values ('5', 'Культура', null);
insert into portfolio.section_settings_ref
values ('6', 'Гражданская активность', null);
insert into portfolio.section_settings_ref
values ('7', 'Успеваемость', '1');
insert into portfolio.section_settings_ref
values ('8', 'Государственная итоговая аттестация', '1');
insert into portfolio.section_settings_ref
values ('9', 'Диагностика', '1');
insert into portfolio.section_settings_ref
values ('10', 'Олимпиады', '1');
insert into portfolio.section_settings_ref
values ('11', 'Награды', '2');
insert into portfolio.section_settings_ref
values ('12', 'Проекты', '2');
insert into portfolio.section_settings_ref
values ('13', 'Занятия', '2');
insert into portfolio.section_settings_ref
values ('14', 'Конкурсы, конференции и иные события',    '2');
insert into portfolio.section_settings_ref
values ('15', 'Награды и разряды', '3');
insert into portfolio.section_settings_ref
values ('16', 'Клубы и команды', '3');
insert into portfolio.section_settings_ref
values ('17', 'Кружки и секции', '3');
insert into portfolio.section_settings_ref
values ('18', 'Соревнования', '3');
insert into portfolio.section_settings_ref
values ('19', 'Походы и экспедиции', '3');
insert into portfolio.section_settings_ref
values ('20', 'Награды и достижения', '4');
insert into portfolio.section_settings_ref
values ('21', 'Творческие коллективы', '4');
insert into portfolio.section_settings_ref
values ('22', 'Конкурсы', '4');
insert into portfolio.section_settings_ref
values ('23', 'Кружки', '4');
insert into portfolio.section_settings_ref
values ('24', 'Посещения учреждений', '5');
insert into portfolio.section_settings_ref
values ('25', 'Онлайн-мероприятия', '5');
insert into portfolio.section_settings_ref
values ('26', 'Награды и статусы', '6');
insert into portfolio.section_settings_ref
values ('27', 'Клубы', '6');
insert into portfolio.section_settings_ref
values ('28', 'Конкурсы, соревнования', '6');
insert into portfolio.section_settings_ref
values ('29', 'Основной государственный экзамен', '8');
insert into portfolio.section_settings_ref
values ('30', 'Единый Государственный экзамен', '8');
insert into portfolio.section_settings_ref
values ('31', 'Государственный выпускной экзамен-9', '8');
insert into portfolio.section_settings_ref
values ('32', 'Государственный выпускной экзамен-11', '8');
insert into portfolio.section_settings_ref
values ('33', 'Другое', '8');

alter table portfolio.administration_setting
    add constraint administration_setting_pk
        primary key (id);

alter table portfolio.administration_setting rename column section_type_code to section_code;

alter table portfolio.administration_setting
    add constraint administration_setting_section_settings_ref_code_fk
        foreign key (section_code) references portfolio.section_settings_ref;



