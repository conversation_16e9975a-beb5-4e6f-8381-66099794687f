create table portfolio.yearly_interest
(
    id                         bigint primary key,
    creation_date              timestamp not null default now(),
    edit_date                  timestamp not null default now(),
    person_id                  varchar   not null,
    interest_count             integer,
    first_interest_name        varchar,
    first_interest_popularity  int,
    second_interest_name       varchar,
    second_interest_popularity int,
    year                       varchar
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_interest()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.person_id IN (SELECT person_id FROM portfolio.yearly_aggregated) THEN
        UPDATE portfolio.yearly_aggregated
        SET first_interest_name = NEW.first_interest_name,
            first_interest_popularity = NEW.first_interest_popularity,
            second_interest_name = NEW.second_interest_name,
            second_interest_popularity = NEW.second_interest_popularity
        WHERE person_id = NEW.person_id;
    ELSE
        INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, first_interest_name,
                                                 first_interest_popularity, second_interest_name, second_interest_popularity, "year")
        VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.first_interest_name, NEW.first_interest_popularity,
                NEW.second_interest_name, NEW.second_interest_popularity, '2023-2024');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER interest_trigger
    AFTER INSERT
    ON portfolio.yearly_interest
    FOR EACH ROW
EXECUTE FUNCTION portfolio.update_aggregated_interest();