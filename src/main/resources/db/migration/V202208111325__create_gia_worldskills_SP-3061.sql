create table portfolio.gia_worldskills
(
    id                 bigint                not null
        constraint gia_worldskills_pkey
            primary key,
    person_id          varchar               not null,
    category_code      smallint
        constraint gia_worldskills_category_code_fkey
            references section_ref,
    source_code        smallint              not null
        constraint gia_worldskills_source_code_fkey
            references data_source_ref,
    creator_id         varchar               not null,
    name               text                  not null,
    file_references    varchar,
    creation_date      timestamp             not null,
    edit_date          timestamp,
    result_date        date                  not null,
    competence_code     varchar               not null,
    result_score        float                 not null,
    max_competence_score float                 not null,
    code               varchar               not null,
    is_early_release   boolean               not null,
    type_code          smallint              not null
        constraint gia_worldskills_type_code_fkey
            references section_ref,
    data_kind          smallint
        constraint gia_worldskills_data_kind_fkey
            references section_ref,
    hash_code          varchar(200),
    is_import          boolean default false not null,
    is_delete          boolean default false not null
);