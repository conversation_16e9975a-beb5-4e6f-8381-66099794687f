ALTER TABLE portfolio.mark_average
    ALTER COLUMN curator_staff_id TYPE int4[]
        USING COALESCE(ARRAY[curator_staff_id], ARRAY[]::int4[]);

ALTER TABLE portfolio.mark_final
    ALTER COLUMN curator_staff_id TYPE int4[]
        USING COALESCE(<PERSON><PERSON><PERSON>[curator_staff_id], ARRAY[]::int4[]);

ALTER TABLE portfolio.mark_final
    ALTER COLUMN staff_id TYPE int4[]
        USING COALESCE(ARRAY[staff_id], ARRAY[]::int4[]);

INSERT INTO portfolio.param_type_ref (code, value)
VALUES (8, 'Array Int');

UPDATE portfolio.param_validation_ref
SET param_type_code = 8
WHERE code IN (70, 98, 102);