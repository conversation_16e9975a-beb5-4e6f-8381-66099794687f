INSERT INTO portfolio.school_proff_classes_link (id, school_code, proff_classes_code, parallel, academic_year, created_date,
                                       edited_date, is_delete)
VALUES (2142, 626, 11, '{8,10}', 23, NOW(), NOW(), FALSE),
       (2143, 498, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2144, 504, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2145, 354, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2146, 377, 11, '{8}', 23, NOW(), NOW(), FALSE),
       (2147, 631, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2148, 489, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2149, 532, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2150, 541, 11, '{7,8,9,10,11}', 23, NOW(), NOW(), FALSE),
       (2151, 548, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2152, 552, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2153, 557, 11, '{7,8,9,10}', 23, NOW(), NOW(), FALSE),
       (2154, 575, 11, '{8}', 23, NOW(), NOW(), FALSE),
       (2155, 593, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2156, 78, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2157, 90, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2158, 92, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2159, 103, 11, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2160, 117, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2161, 135, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2162, 141, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2163, 145, 11, '{10,11}', 23, NOW(), NOW(), FALSE),
       (2164, 162, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2165, 167, 11, '{7,8,9,10,11}', 23, NOW(), NOW(), FALSE),
       (2166, 189, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2167, 202, 11, '{7,8,9,11}', 23, NOW(), NOW(), FALSE),
       (2168, 210, 11, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2169, 230, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2170, 233, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2171, 255, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2172, 261, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2173, 277, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2174, 335, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2175, 338, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2176, 343, 11, '{11}', 23, NOW(), NOW(), FALSE),
       (2177, 373, 11, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2178, 393, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2179, 397, 11, '{8,10}', 23, NOW(), NOW(), FALSE),
       (2180, 399, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2181, 421, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2182, 429, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2183, 432, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2184, 441, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2185, 445, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2186, 452, 11, '{7,8,9,10,11}', 23, NOW(), NOW(), FALSE),
       (2187, 468, 11, '{9,10}', 23, NOW(), NOW(), FALSE),
       (2188, 470, 11, '{9,10,11}', 23, NOW(), NOW(), FALSE),
       (2189, 74, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2190, 52, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2191, 58, 11, '{7}', 23, NOW(), NOW(), FALSE),
       (2192, 46, 11, '{9}', 23, NOW(), NOW(), FALSE),
       (2193, 352, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2194, 498, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2195, 504, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2196, 576, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2197, 109, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2198, 214, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2199, 268, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2200, 379, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2201, 403, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2202, 471, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2203, 480, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2204, 482, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2205, 489, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2206, 491, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2207, 502, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2208, 513, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2209, 514, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2210, 516, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2211, 517, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2212, 523, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2213, 524, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2214, 525, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2215, 526, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2216, 532, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2217, 533, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2218, 538, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2219, 544, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2220, 548, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2221, 550, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2222, 552, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2223, 563, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2224, 565, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2225, 569, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2226, 571, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2227, 587, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2228, 588, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2229, 595, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2230, 598, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2231, 602, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2232, 83, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2233, 90, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2234, 93, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2235, 99, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2236, 100, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2237, 101, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2238, 102, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2239, 103, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2240, 105, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2241, 107, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2242, 112, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2243, 113, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2244, 115, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2245, 116, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2246, 119, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2247, 121, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2248, 131, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2249, 135, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2250, 139, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2251, 148, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2252, 150, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2253, 154, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2254, 155, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2255, 164, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2256, 166, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2257, 169, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2258, 176, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2259, 177, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2260, 180, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2261, 181, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2262, 184, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2263, 187, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2264, 190, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2265, 191, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2266, 194, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2267, 197, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2268, 199, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2269, 204, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2270, 210, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2271, 215, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2272, 216, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2273, 218, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2274, 220, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2275, 221, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2276, 229, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2277, 230, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2278, 231, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2279, 233, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2280, 235, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2281, 236, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2282, 237, 12, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2283, 238, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2284, 239, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2285, 240, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2286, 245, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2287, 31, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2288, 251, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2289, 253, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2290, 255, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2291, 258, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2292, 259, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2293, 266, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2294, 267, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2295, 273, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2296, 279, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2297, 280, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2298, 12, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2299, 287, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2300, 291, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2301, 296, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2302, 297, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2303, 300, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2304, 302, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2305, 305, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2306, 26, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2307, 315, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2308, 316, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2309, 317, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2310, 318, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2311, 321, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2312, 327, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2313, 328, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2314, 329, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2315, 330, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2316, 338, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2317, 343, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2318, 348, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2319, 351, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2320, 361, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2321, 363, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2322, 366, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2323, 380, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2324, 381, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2325, 382, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2326, 384, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2327, 386, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2328, 397, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2329, 404, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2330, 406, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2331, 409, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2332, 410, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2333, 412, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2334, 415, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2335, 419, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2336, 421, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2337, 422, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2338, 425, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2339, 426, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2340, 427, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2341, 428, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2342, 432, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2343, 434, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2344, 435, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2345, 441, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2346, 442, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2347, 443, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2348, 444, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2349, 445, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2350, 447, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2351, 451, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2352, 452, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2353, 454, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2354, 457, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2355, 460, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2356, 461, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2357, 468, 12, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2358, 32, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2359, 33, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2360, 36, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2361, 48, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2362, 45, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2363, 52, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2364, 65, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2365, 46, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2366, 54, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2367, 58, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2368, 60, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2369, 63, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2370, 66, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2371, 70, 12, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2372, 71, 12, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2373, 610, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2374, 617, 12, '{7}', 23, NOW(), NOW(), FALSE),
       (2375, 555, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2376, 511, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2377, 528, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2378, 214, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2379, 354, 13, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2380, 379, 13, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2381, 403, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2382, 477, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2383, 478, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2384, 480, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2385, 489, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2386, 492, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2387, 514, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2388, 517, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2389, 523, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2390, 525, 13, '{8,9}', 23, NOW(), NOW(), FALSE),
       (2391, 536, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2392, 538, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2393, 539, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2394, 541, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2395, 544, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2396, 546, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2397, 548, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2398, 552, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2399, 556, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2400, 557, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2401, 563, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2402, 565, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2403, 569, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2404, 574, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2405, 583, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2406, 584, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2407, 586, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2408, 587, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2409, 591, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2410, 592, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2411, 599, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2412, 601, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2413, 602, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2414, 83, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2415, 84, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2416, 85, 13, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2417, 102, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2418, 103, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2419, 113, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2420, 115, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2421, 116, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2422, 117, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2423, 119, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2424, 121, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2425, 127, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2426, 129, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2427, 131, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2428, 132, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2429, 139, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2430, 145, 13, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2431, 148, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2432, 151, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2433, 152, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2434, 153, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2435, 155, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2436, 160, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2437, 161, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2438, 162, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2439, 169, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2440, 170, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2441, 172, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2442, 176, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2443, 177, 13, '{7,9}', 23, NOW(), NOW(), FALSE),
       (2444, 178, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2445, 179, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2446, 181, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2447, 182, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2448, 187, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2449, 193, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2450, 194, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2451, 195, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2452, 197, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2453, 203, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2454, 206, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2455, 210, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2456, 215, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2457, 216, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2458, 218, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2459, 220, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2460, 221, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2461, 223, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2462, 225, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2463, 226, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2464, 229, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2465, 230, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2466, 235, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2467, 236, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2468, 238, 13, '{7,8,9}', 23, NOW(), NOW(), FALSE),
       (2469, 240, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2470, 242, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2471, 245, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2472, 251, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2473, 255, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2474, 258, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2475, 259, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2476, 263, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2477, 266, 13, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2478, 267, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2479, 271, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2480, 274, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2481, 275, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2482, 277, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2483, 279, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2484, 282, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2485, 283, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2486, 12, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2487, 288, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2488, 295, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2489, 305, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2490, 306, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2491, 26, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2492, 327, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2493, 328, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2494, 329, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2495, 333, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2496, 335, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2497, 343, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2498, 348, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2499, 358, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2500, 363, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2501, 366, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2502, 371, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2503, 374, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2504, 376, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2505, 381, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2506, 391, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2507, 393, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2508, 395, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2509, 397, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2510, 398, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2511, 406, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2512, 409, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2513, 410, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2514, 412, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2515, 415, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2516, 420, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2517, 421, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2518, 423, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2519, 425, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2520, 432, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2521, 434, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2522, 435, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2523, 438, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2524, 441, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2525, 444, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2526, 447, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2527, 450, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2528, 452, 13, '{8}', 23, NOW(), NOW(), FALSE),
       (2529, 453, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2530, 454, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2531, 457, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2532, 461, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2533, 463, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2534, 466, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2535, 468, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2536, 472, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2537, 61, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2538, 69, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2539, 33, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2540, 36, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2541, 48, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2542, 45, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2543, 65, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2544, 46, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2545, 56, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2546, 66, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2547, 71, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2548, 72, 13, '{7}', 23, NOW(), NOW(), FALSE),
       (2549, 93, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2550, 146, 16, '{7,8}', 23, NOW(), NOW(), FALSE),
       (2551, 189, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2552, 210, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2553, 258, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2554, 315, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2555, 343, 16, '{7}', 23, NOW(), NOW(), FALSE),
       (2556, 452, 16, '{7}', 23, NOW(), NOW(), FALSE);