create table if not exists portfolio.exam_form_ref
(
    code  smallint primary key,
    value text
);

insert into portfolio.exam_form_ref values (0, 'ГВЭ-9');
insert into portfolio.exam_form_ref values (1, 'ГВЭ-11');
insert into portfolio.exam_form_ref values (2, 'ЕГЭ');
insert into portfolio.exam_form_ref values (3, 'ОГЭ');
insert into portfolio.exam_form_ref values (4, 'Итоговое сочинение');
insert into portfolio.exam_form_ref values (5, 'Итоговое изложение');
insert into portfolio.exam_form_ref values (6, 'Итоговое собеседование по русскому языку');

CREATE INDEX if not exists exam_form_ref_value_trigram_index
    ON portfolio.exam_form_ref
        USING gin (value gin_trgm_ops);