create table if not exists portfolio.yearly_success_subject
(
    id            bigint primary key default nextval('portfolio.hibernate_sequence'),
    creation_date timestamp not null default now(),
    edit_date     timestamp not null default now(),
    person_id     varchar   not null,
    subject       varchar   not null,
    mark          varchar   not null,
    year          varchar            default '2023-2024'
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_success_subject()
    RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             success_subject, success_subject_mark, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           subject,
           mark,
           '2023-2024'
    FROM portfolio.yearly_success_subject
    ON CONFLICT (person_id, "year") DO UPDATE
        SET success_subject      = EXCLUDED.success_subject,
            success_subject_mark = EXCLUDED.success_subject_mark;
    RETURN true;
END;
$$ LANGUAGE plpgsql;