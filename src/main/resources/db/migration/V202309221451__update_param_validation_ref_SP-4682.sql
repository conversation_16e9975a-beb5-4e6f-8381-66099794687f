alter table portfolio.param_validation_ref
add param_type_code Int;

update portfolio.param_validation_ref set param_type_code = 3 where code = 1;
update portfolio.param_validation_ref set param_type_code = 3 where code = 2;
update portfolio.param_validation_ref set param_type_code = 3 where code = 3;
update portfolio.param_validation_ref set param_type_code = 3 where code = 4;
update portfolio.param_validation_ref set param_type_code = 3 where code = 5;
update portfolio.param_validation_ref set param_type_code = 3 where code = 6;
update portfolio.param_validation_ref set param_type_code = 3 where code = 7;
update portfolio.param_validation_ref set param_type_code = 3 where code = 8;
update portfolio.param_validation_ref set param_type_code = 3 where code = 9;
update portfolio.param_validation_ref set param_type_code = 2 where code = 10;
update portfolio.param_validation_ref set param_type_code = 2 where code = 11;
update portfolio.param_validation_ref set param_type_code = 2 where code = 12;
update portfolio.param_validation_ref set param_type_code = 2 where code = 13;
update portfolio.param_validation_ref set param_type_code = 2 where code = 14;
update portfolio.param_validation_ref set param_type_code = 2 where code = 15;
update portfolio.param_validation_ref set param_type_code = 2 where code = 16;
update portfolio.param_validation_ref set param_type_code = 2 where code = 17;
update portfolio.param_validation_ref set param_type_code = 2 where code = 18;
update portfolio.param_validation_ref set param_type_code = 2 where code = 19;
update portfolio.param_validation_ref set param_type_code = 2 where code = 20;
update portfolio.param_validation_ref set param_type_code = 2 where code = 21;
update portfolio.param_validation_ref set param_type_code = 2 where code = 22;
update portfolio.param_validation_ref set param_type_code = 2 where code = 23;
update portfolio.param_validation_ref set param_type_code = 2 where code = 24;
update portfolio.param_validation_ref set param_type_code = 2 where code = 25;
update portfolio.param_validation_ref set param_type_code = 2 where code = 26;
update portfolio.param_validation_ref set param_type_code = 2 where code = 27;
update portfolio.param_validation_ref set param_type_code = 2 where code = 28;
update portfolio.param_validation_ref set param_type_code = 2 where code = 29;
update portfolio.param_validation_ref set param_type_code = 2 where code = 30;
update portfolio.param_validation_ref set param_type_code = 2 where code = 31;
update portfolio.param_validation_ref set param_type_code = 2 where code = 32;
update portfolio.param_validation_ref set param_type_code = 2 where code = 33;
update portfolio.param_validation_ref set param_type_code = 2 where code = 34;
update portfolio.param_validation_ref set param_type_code = 2 where code = 35;
update portfolio.param_validation_ref set param_type_code = 3 where code = 36;
update portfolio.param_validation_ref set param_type_code = 3 where code = 37;
update portfolio.param_validation_ref set param_type_code = 3 where code = 38;

insert into portfolio.param_validation_ref (code, kafka_event_code, param_name, param_type_code, is_not_null) values
(39,3,'achievement.achievementId',1,TRUE),
(40,3,'achievement.personId',3,TRUE),
(41,3,'achievement.dateOfAward',4,TRUE),
(42,3,'achievement.contest.name',3,TRUE),
(43,3,'achievement.contest.startDate',4,TRUE),
(44,3,'achievement.contest.endDate',4,TRUE),
(45,3,'achievement.organizationId',1,FALSE),
(46,3,'achievement.organizationName',3,FALSE);