delete
from employment
where category_code = 39
   or category_code = 66
   or type_code = 39
   or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

delete
from event
where category_code = 39
   or category_code = 66
   or type_code = 39
   or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

delete
from reward
where category_code = 39
   or category_code = 66
   or type_code = 39
   or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

delete
from sport_reward
where category_code = 39
   or category_code = 66
   or type_code = 39
   or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

delete
from affilation
where category_code = 39
   or category_code = 66
   or type_code = 39
   or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

delete
from project
where category_code = 39
   or category_code = 66
   --  or type_code = 39
   --  or type_code = 66
   or data_kind = 39
   or data_kind = 66
;

update section_ref
set value = 'Культура'
where code = 5;

update section_ref
set parent_id = 45,
    value = 'Награда за творческий конкурс'
where code = 46;

delete
from section_ref
where code = 66;

delete
from section_ref
where code = 39;
