create table if not exists portfolio.favorite_university
(
    id            bigint    not null
        constraint favorite_university_pkey
            primary key,
    person_id     varchar   not null,
    university_id integer   not null,
    creation_date timestamp not null,
    edit_date     timestamp,
    source_code   smallint
        constraint favorite_university_source_code_fkey
            references data_source_ref,
    creator_id    varchar   not null,
    is_delete     boolean default false
);