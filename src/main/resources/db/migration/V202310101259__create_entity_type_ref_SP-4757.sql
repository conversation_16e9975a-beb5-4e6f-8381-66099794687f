create table portfolio.entity_type_ref
(
    code        int
        constraint entity_type_ref_pk
            primary key,
    value       varchar not null,
    description varchar
);

INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (1, 'Event', 'Мероприятие');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (2, 'Employment', 'Занятость (кружки)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (3, 'Reward', 'Награда (любая категория, кроме спортивных наград)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (4, 'SportReward', 'Спортивная награда');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (5, 'Project', 'Проект');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (6, 'Affilation', 'Принадлежность (клубы, команда)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (7, 'GIAWorldskills', 'ГИА ДЭ WorldSkills');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (8, 'Job', 'Трудоустройство');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (9, 'Document', 'Документы (СПО)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (10, 'CheckIn', 'Чекины');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (11, 'TrainingInfo', 'Сведения об обучении (СПО)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (12, 'Practice', 'Практика СПО');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (13, 'Exam', 'ГИА (ОГЭ, ЕГЭ, ...)');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (14, 'IndependentDiagnostics', 'Независимые диагностики');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (15, 'SelfDiagnostics', 'Самодиагностики');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (16, 'CulturalEvent', 'Посещение культурных мероприятий');
INSERT INTO portfolio.entity_type_ref (code, value, description)
VALUES (17, 'AdditionalEducation', 'Дополнительное образование (Профподготовка)');