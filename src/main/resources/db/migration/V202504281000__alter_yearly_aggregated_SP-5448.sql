ALTER TABLE portfolio.yearly_aggregated
--Вариант сообщения
ADD COLUMN message_variant integer,

--Количество заданного ДЗ
ADD COLUMN homework_count integer,

--Количество посещенных уроков
ADD COLUMN passed_lesson_count integer,

--Количество купленных товаров
ADD COLUMN count_product integer,
--Количество совершенных покупок
ADD COLUMN count_buy integer,
--Наименование купленной позиции буфета
ADD COLUMN first_position_name varchar,
--Количество раз покупки позиции буфета
ADD COLUMN first_position_count varchar,
ADD COLUMN second_position_name varchar,
ADD COLUMN second_position_count varchar,
ADD COLUMN third_position_name varchar,
ADD COLUMN third_position_count varchar,

-- Количество всех полученных учеником оценок
ADD COLUMN marks_total integer,
-- Популярная оценка
ADD COLUMN marks_popular integer,
-- Количество популярной оценки
ADD COLUMN marks_popular_amount integer,
-- Параллель
ADD COLUMN parallel_level integer,
-- Признак того, один или несколько предметов с одинаковым средним балом
ADD COLUMN not_one boolean DEFAULT false,
-- Признак успешной отправки сообщений в kafka
ADD COLUMN is_sent boolean DEFAULT false;
