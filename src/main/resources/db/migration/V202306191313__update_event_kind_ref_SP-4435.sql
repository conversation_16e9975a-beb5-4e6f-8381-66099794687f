insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (967,16,'Конкурс-акселератор инновационных проектов "Большая разведка"',null,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (968,16,'Междисциплинарный общеуниверситетский конкурс "Проект по педагогике"',null,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (969,28,'Российский открытый молодежный водный конкурс',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (970,44,'Образовательно-туристский проект «По пути великих открытий»',4,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (971,56,'Экологический марафон, посвященный науки и технологиям, среди обучающихся профессиональных образовательных учреждений РФ',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (972,57,'Конкурс юных инспекторов дорожного движения "Безопасное колесо"',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (973,70,'Московский Чемпионат профессионального мастерства для людей с инвалидностью и ограниченными возможностями здоровья "Абилимпикс"',4,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (974,70,'Национальный Чемпионат профессионального мастерства для людей с инвалидностью и ограниченными возможностями здоровья "Абилимпикс',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (976,70,'Московский детский чемпионат "Мастерята" (бывш. KidSkills)',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (978,70,'Национальный чемпионат сквозных рабочих профессий высокотехнологичных отраслей промышленности "Хайтек" (по стандартам Ворлдскиллс)',4,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (979,70,'Отраслевой чемпионат в сфере информационных технологий DigitalSkills (по стандартам Ворлдскиллс)',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (980,70,'Чемпионат по профессиональному мастерству «Профессионалы»',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (981,70,'Чемпионат высоких технологий',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (982,70,'Многопрофильная олимпиада ТОГУ для обучающихся средних профессиональных учреждений',5,null);
insert into portfolio.event_kind_ref (code, category_code, value, level_event, organizators) values (966,55,'Мой героический район',4,null);
update portfolio.event_kind_ref set is_archive = true where code in (746);
update portfolio.event_kind_ref set category_code = 70 where code in (955,958,961,962,963,965);
update portfolio.event_kind_ref set is_archive = true where code in (739,740,741,742,743,912,913);
insert into portfolio.interest_media_social_kind_ref (code, value, parent_id, interest_group_code, interest_action_code) values (10,'Новости',null,null,'{63,64,65,66}')
