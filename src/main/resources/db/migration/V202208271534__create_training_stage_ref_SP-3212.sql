CREATE TABLE portfolio.training_stage_ref
(
    code      INTEGER     NOT NULL PRIMARY KEY,
    value     VARCHAR(50) NOT NULL,
    parent_id INTEGER
);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (1, 'Этап начальной подготовки', NULL);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (2, 'Тренировочный этап (Этап спортивной специализации)', NULL);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (3, 'Этап совершенствования спортивного мастерства', NULL);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (4, 'Этап высшего спортивного мастерства', NULL);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (5, 'ЭНП 1', 1);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (6, 'ЭНП 2', 1);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (7, 'ЭНП 3', 1);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (8, 'ЭНП 4', 1);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (9, 'ТЭ 1', 2);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (10, 'ТЭ 2', 2);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (11, 'ТЭ 3', 2);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (12, 'ТЭ 4', 2);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (13, 'ТЭ 5', 2);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (14, 'ЭССМ 1', 3);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (15, 'ЭССМ 2', 3);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (16, 'ЭССМ 3', 3);
INSERT INTO portfolio.training_stage_ref(code, value, parent_id)
VALUES (17, 'ЭВСМ', 4);
