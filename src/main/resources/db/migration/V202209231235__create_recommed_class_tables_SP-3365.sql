create table portfolio.school
(
    code int
        constraint schools_pk
            primary key,
    nsi_id int,
    district_id int,
    short_name varchar(255) not null,
    is_archive boolean
);

create index school_district_id_index
    on portfolio.school (district_id);

create index school_nsi_id_index
    on portfolio.school (nsi_id);

create table portfolio.proff_classes_ref
(
    code int
        constraint proff_classes_ref_pk
            primary key,
    value varchar(255) not null,
    link_info1 varchar(255),
    link_info2 varchar(255),
    is_archive boolean
);

create index proff_classes_ref_value_index
    on portfolio.proff_classes_ref (value);

create table portfolio.interest_action_recommend
(
    id int
        constraint interest_action_recommend_pk
            primary key,
    head_interest_code int not null
        constraint interest_action_recommend_interest_head_kind_ref_code_fk
            references portfolio.interest_head_kind_ref,
    interest_code int not null,
    interest_action_code int[] not null,
    proff_classes_code int[] not null,
    is_delete boolean
);

create index interest_action_recommend_head_interest_code_index
    on portfolio.interest_action_recommend (head_interest_code);

create index interest_action_recommend_interest_code_index
    on portfolio.interest_action_recommend (interest_code);

create table portfolio.school_proff_classes_link
(
    id int
        constraint school_proff_classes_link_pk
            primary key,
    school_code int not null,
    proff_classes_code int not null,
    parallel int[] not null,
    academic_year int not null,
    created_date timestamp not null,
    edited_date timestamp not null,
    is_delete boolean
);

alter table portfolio.school_proff_classes_link
    add constraint school_fk
        foreign key (school_code) references portfolio.school;

alter table portfolio.school_proff_classes_link
    add constraint proff_classes_fk
        foreign key (proff_classes_code) references portfolio.proff_classes_ref;


create index school_proff_classes_link_academic_year_index
    on portfolio.school_proff_classes_link (academic_year);

create index school_proff_classes_link_parallel_index
    on portfolio.school_proff_classes_link (parallel);

create index school_proff_classes_link_proff_classes_code_index
    on portfolio.school_proff_classes_link (proff_classes_code);

create index school_proff_classes_link_school_code_index
    on portfolio.school_proff_classes_link (school_code);


