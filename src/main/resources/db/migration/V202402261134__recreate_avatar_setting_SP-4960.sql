drop table if exists portfolio.avatar_setting;

CREATE TABLE if not exists portfolio.avatar_setting
(
    id               bigint                not null
        constraint avatar_setting_pkey
            primary key,
    creation_date    timestamp             not null,
    edit_date        timestamp             not null,
    avatar_type_code smallint
        constraint avatar_setting_avatar_type_fkey
            references portfolio.avatar_type_ref,
    user_id          bigint                not null,
    person_id        varchar               not null,
    is_delete        boolean default false not null
);