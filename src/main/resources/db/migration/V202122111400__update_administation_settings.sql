alter table portfolio.administration_setting
    rename column permission_id to permission_code;

alter table portfolio.administration_setting
    drop column if exists is_delete;

alter table portfolio.administration_setting
    drop column if exists valid_from;

alter table portfolio.administration_setting
    add if not exists parent_section_id smallint;

alter table portfolio.administration_setting
    drop column if exists valid_to;

alter table portfolio.administration_setting
    drop column if exists creation_date;

alter table portfolio.administration_setting
    alter column administrator_user_id type varchar using administrator_user_id::varchar;

create table portfolio.section_settings_ref
(
    code      smallint
        constraint section_settings_ref_pk
            primary key,
    value     varchar(250) not null,
    parent_id smallint
);

create table portfolio.administration_settings_log
(
    id                          bigint
        constraint administration_settings_log_pk
            primary key,
    section_id                  smallint     not null,
    learner_category_codes      varchar(50),
    administrator_user_id       varchar(500) not null,
    comment                     varchar(500),
    prev_learner_category_codes varchar(50),
    creation_date               timestamp    not null,
    action_type                 varchar(50)  not null,
    parent_section_id           smallint
);

