create table portfolio.yearly_achievements
(
    id                     bigint primary key,
    creation_date          timestamp not null default now(),
    edit_date              timestamp not null default now(),
    person_id              varchar   not null,
    achievements_count     integer,
    first_achievement_date date,
    year                   varchar
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_achievement()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.person_id IN (SELECT person_id FROM portfolio.yearly_aggregated) THEN
        UPDATE portfolio.yearly_aggregated
        SET achievements_count     = NEW.achievements_count,
            first_achievement_date = NEW.first_achievement_date
        WHERE person_id = NEW.person_id;
    ELSE
        INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, achievements_count,
                                                 first_achievement_date, "year")
        VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.achievements_count,
                NEW.first_achievement_date, '2023-2024');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER achievement_trigger
    AFTER INSERT
    ON portfolio.yearly_achievements
    FOR EACH ROW
EXECUTE FUNCTION portfolio.update_aggregated_achievement();
