create table if not exists portfolio.yearly_homework
(
    id            bigint primary key default nextval('portfolio.hibernate_sequence'),
    creation_date timestamp not null default now(),
    edit_date     timestamp not null default now(),
    person_id     varchar   not null,
    homework_count    integer   not null,
    year          varchar            default '2024-2025'
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_homework()
    RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             homework_count, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           homework_count,
           '2024-2025'
    FROM portfolio.yearly_homework
    ON CONFLICT (person_id, "year") DO UPDATE
        SET homework_count = EXCLUDED.homework_count;
    RETURN true;
END;
$$ LANGUAGE plpgsql;