CREATE OR REPLACE FUNCTION portfolio.update_aggregated_buy()
    RET<PERSON>NS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date,
    edit_date, person_id, count_product, count_buy,
    first_position_name, first_position_count,
    second_position_name, second_position_count,
    third_position_name, third_position_count,
    "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           count_product,
           count_buy,
           first_position_name,
           first_position_count,
           second_position_name,
           second_position_count,
           third_position_name,
           third_position_count,
           '2024-2025'
    FROM portfolio.yearly_buy
    ON CONFLICT (person_id, "year") DO UPDATE
        SET count_product = EXCLUDED.count_product,
            count_buy = EXCLUDED.count_buy,
            first_position_name = EXCLUDED.first_position_name,
            first_position_count = EXCLUDED.first_position_count,
            second_position_name = EXCLUDED.second_position_name,
            second_position_count = EXCLUDED.second_position_count,
            third_position_name = EXCLUDED.third_position_name,
            third_position_count = EXCLUDED.third_position_count;
    RETUR<PERSON> true;
END;
$$ LANGUAGE plpgsql;
