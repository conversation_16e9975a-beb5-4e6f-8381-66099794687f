update portfolio.project set data_kind = 11 where data_kind is null;
update portfolio.reward set data_kind = 9 where data_kind is null and source_code = 15;

ALTER TABLE portfolio.event alter column category_code set not null;
ALTER TABLE portfolio.event alter column data_kind set not null;
ALTER TABLE portfolio.event alter column type_code set not null;

ALTER TABLE portfolio.affilation alter column category_code set not null;
ALTER TABLE portfolio.affilation alter column data_kind set not null;
ALTER TABLE portfolio.affilation alter column type_code set not null;

ALTER TABLE portfolio.employment alter column category_code set not null;
ALTER TABLE portfolio.employment alter column data_kind set not null;
ALTER TABLE portfolio.employment alter column type_code set not null;

ALTER TABLE portfolio.project  alter column category_code set not null;
ALTER TABLE portfolio.project  alter column data_kind set not null;

ALTER TABLE portfolio.reward alter column category_code set not null;
ALTER TABLE portfolio.reward alter column data_kind set not null;
ALTER TABLE portfolio.reward alter column type_code set not null;

ALTER TABLE portfolio.sport_reward alter column category_code set not null;
ALTER TABLE portfolio.sport_reward alter column data_kind set not null;
ALTER TABLE portfolio.sport_reward alter column type_code set not null;

ALTER TABLE portfolio.check_in_history alter column category_code set not null;
ALTER TABLE portfolio.check_in_history alter column data_kind set not null;
ALTER TABLE portfolio.check_in_history alter column type_code set not null;

ALTER TABLE portfolio.gia_worldskills alter column category_code set not null;
ALTER TABLE portfolio.gia_worldskills alter column data_kind set not null;
ALTER TABLE portfolio.gia_worldskills alter column type_code set not null;