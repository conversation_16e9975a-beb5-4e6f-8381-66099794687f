alter table portfolio.share_link
    alter column start_date type timestamptz,
    alter column end_date type timestamptz,
    alter column creation_date type timestamptz;

alter table portfolio.share_link
    add column if not exists gve9  boolean default true,
    add column if not exists gve11 boolean default true,
    add column if not exists other boolean default true;

alter table portfolio.share_link
    alter column url set not null,
    alter column start_date set not null,
    alter column end_date set not null,
    alter column creation_date set not null,
    alter column is_active set not null,
    alter column academic set not null,
    alter column govexams set not null,
    alter column self_training set not null,
    alter column library set not null ,
    alter column self_diagnostic set not null ,
    alter column home_work set not null,
    alter column achievements set not null,
    alter column olympiad set not null,
    alter column study set not null,
    alter column student_data set not null;

alter table portfolio.user_setting
    alter column creation_date set not null;
