alter table portfolio.sport_age_ref
    add is_archive boolean default false;

UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 1;
UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 2;
UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 3;
UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 4;
UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 5;
UPDATE portfolio.sport_age_ref SET is_archive = TRUE WHERE code = 6;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE code = 7;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE code = 8;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE code = 9;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE code = 10;
UPDATE portfolio.sport_age_ref SET is_archive = FALSE WHERE code = 11;


INSERT INTO portfolio.sport_age_ref (code, value,section_code, is_archive) VALUES
(12,'1 ступень— от 6 до 7 лет',32,FALSE),
(13,'2 ступень — от 8 до 9 лет',32,FALSE),
(14,'3 ступень— от 10 до 11 лет',32,FALSE),
(15,'4 ступень — от 12 до 13 лет',32,FALSE),
(16,'5 ступень — от 14 до 15 лет',32,FALSE),
(17,'6 ступень — от 16 до 17 лет',32,FALSE),
(18,'7 ступень — от 18 до 19 лет',32,FALSE),
(19,'8 ступень — от 20 до 24 лет',32,FALSE),
(20,'9 ступень — от 25 до 29 лет',32,FALSE);