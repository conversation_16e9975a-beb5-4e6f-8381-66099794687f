update portfolio.sport_kind_ref
set parent_id = 4280
where parent_id = 4273;

delete
from portfolio.sport_kind_ref
where code in (4274, 4275, 4276, 4277, 4278, 4279);

INSERT INTO "portfolio"."sport_kind_ref" ("code", "parent_id", "value", "code_type_sport")
    VALUES (5876, 4280, 'чир - фристайл - группа', '1040041811Я');

update portfolio.affilation
set sport_kind_code = 4280 where sport_kind_code = 4273;

update portfolio.employment
set sport_kind_code = 4280 where sport_kind_code = 4273;

update portfolio.event
set sport_kind_code = 4280 where sport_kind_code = 4273;

update portfolio.sport_reward
set sport_kind_code = 4280 where sport_kind_code = 4273;