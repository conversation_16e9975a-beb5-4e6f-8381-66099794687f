ALTER TABLE portfolio.yearly_marks
ADD COLUMN marks_total         integer,
ADD COLUMN marks_popular       integer,
ADD COLUMN marks_popular_amount integer,
ADD COLUMN parallel_level integer;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_marks()
RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (
        id,
        creation_date,
        edit_date,
        person_id,
        marks_message_variant,
        marks_good_amount,
        marks_excellent_amount,
        marks_total,
        marks_popular,
        marks_popular_amount,
        parallel_level,
        "year"
    )
    SELECT
        nextval('hibernate_sequence'),
        now(),
        now(),
        person_id,
        message_variant,
        good_amount,
        excellent_amount,
        marks_total,
        marks_popular,
        marks_popular_amount,
        parallel_level,
        '2024-2025'
    FROM portfolio.yearly_marks
    ON CONFLICT (person_id, "year") DO UPDATE
    SET
        marks_message_variant   = EXCLUDED.marks_message_variant,
        marks_good_amount       = EXCLUDED.marks_good_amount,
        marks_excellent_amount  = EXCLUDED.marks_excellent_amount,
        marks_total             = EXCLUDED.marks_total,
        marks_popular           = EXCLUDED.marks_popular,
        marks_popular_amount    = EXCLUDED.marks_popular_amount,
        parallel_level          = EXCLUDED.parallel_level;


    RETURN true;
END;
$$ LANGUAGE plpgsql;

