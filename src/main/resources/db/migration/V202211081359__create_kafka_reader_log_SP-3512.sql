create table portfolio.kafka_reader_log
(
    id                bigint
        constraint kafka_reader_log_pk
            primary key,
    person_id         varchar(255),
    log_date_time     timestamp,
    log_result        varchar(255),
    action_type_code  int,
    action_kind_code  int,
    entity_id         bigint,
    entity_type       varchar(255),
    source_code       int,
    error_log_message varchar(255)
);

create table  portfolio.kafka_message_processed
(
    id      bigint
        constraint kafka_message_processed_pk
            primary key,
    message json
);

alter table portfolio.kafka_reader_log
    add message_id bigint;

alter table portfolio.kafka_reader_log
    add constraint kafka_reader_log_kafka_message_processed_id_fk
        foreign key (message_id)
            references portfolio.kafka_message_processed(id);