CREATE OR REPLACE FUNCTION portfolio.update_aggregated_success_subject()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             success_subject, success_subject_mark, not_one, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           subject,
           mark,
           not_one,
           '2024-2025'
    FROM portfolio.yearly_success_subject
    where "year"='2024-2025'
    ON CONFLICT (person_id, "year") DO UPDATE
        SET success_subject      = EXCLUDED.success_subject,
            success_subject_mark = EXCLUDED.success_subject_mark;
    RETURN true;
END;
$function$
;