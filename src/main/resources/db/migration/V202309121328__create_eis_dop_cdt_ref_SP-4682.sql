create table portfolio.eis_dop_cdt_ref
(
    code                       int
        constraint eis_dop_cdt_ref_pk
            primary key,
    contest_orientation        int,
    contest_type               int,
    event_category_code        int
        constraint eis_dop_cdt_event_category_code_fkey
            references portfolio.section_ref,
    event_data_kind            int
        constraint eis_dop_cdt_event_data_kind_fkey
            references portfolio.section_ref,
    event_type_code            int
        constraint eis_dop_cdt_event_type_code_fkey
            references portfolio.section_ref,
    reward_category_code       int
        constraint eis_dop_cdt_reward_category_code_fkey
            references portfolio.section_ref,
    reward_data_kind           int
        constraint eis_dop_cdt_reward_data_kind_fkey
            references portfolio.section_ref,
    reward_type_code           int
        constraint eis_dop_cdt_reward_type_code_fkey
            references portfolio.section_ref,
    sport_reward_category_code int
        constraint eis_dop_cdt_sport_reward_category_code_fkey
            references portfolio.section_ref,
    sport_reward_data_kind     int
        constraint eis_dop_cdt_sport_reward_data_kind_fkey
            references portfolio.section_ref,
    sport_reward_type_code     int
        constraint eis_dop_cdt_sport_reward_type_code_fkey
            references portfolio.section_ref
);

INSERT INTO portfolio.eis_dop_cdt_ref (code,contest_orientation,contest_type,event_category_code,event_data_kind,event_type_code,reward_category_code,reward_data_kind,reward_type_code,sport_reward_category_code,sport_reward_data_kind,sport_reward_type_code) VALUES
(1,166,6,4,43,86,4,45,146,NULL,NULL,NULL),
(2,166,1,4,43,87,4,45,147,NULL,NULL,NULL),
(3,166,2,4,43,88,4,45,148,NULL,NULL,NULL),
(4,166,3,4,43,89,4,45,149,NULL,NULL,NULL),
(5,166,4,4,43,90,4,45,150,NULL,NULL,NULL),
(6,166,5,4,43,91,4,45,151,NULL,NULL,NULL),
(7,166,7,4,43,92,4,45,152,NULL,NULL,NULL),
(8,166,8,4,43,93,4,45,153,NULL,NULL,NULL),
(9,166,9,4,43,94,4,45,154,NULL,NULL,NULL),
(10,166,10,4,43,95,4,45,155,NULL,NULL,NULL),
(11,166,11,4,43,96,4,45,156,NULL,NULL,NULL),
(12,166,12,4,43,97,4,45,157,NULL,NULL,NULL),
(13,166,13,4,43,98,4,45,158,NULL,NULL,NULL),
(14,166,14,4,43,99,4,45,159,NULL,NULL,NULL),
(15,166,16,4,43,100,4,45,160,NULL,NULL,NULL),
(16,166,15,4,43,101,4,45,161,NULL,NULL,NULL),
(17,165,1,2,14,102,2,17,163,NULL,NULL,NULL),
(18,165,2,2,14,103,2,17,164,NULL,NULL,NULL),
(19,165,5,2,14,104,2,17,165,NULL,NULL,NULL),
(20,165,7,2,14,105,2,17,166,NULL,NULL,NULL),
(21,165,8,2,14,106,2,17,167,NULL,NULL,NULL),
(22,165,9,2,14,107,2,17,168,NULL,NULL,NULL),
(23,165,10,2,14,108,2,17,169,NULL,NULL,NULL),
(24,165,11,2,14,109,2,17,170,NULL,NULL,NULL),
(25,165,12,2,14,110,2,17,171,NULL,NULL,NULL),
(26,165,13,2,14,111,2,17,172,NULL,NULL,NULL),
(27,165,14,2,14,112,2,17,173,NULL,NULL,NULL),
(28,165,16,2,14,113,2,17,174,NULL,NULL,NULL),
(29,165,15,2,14,114,2,17,175,NULL,NULL,NULL),
(30,165,6,2,14,207,2,17,162,NULL,NULL,NULL),
(31,173,1,2,14,102,2,17,163,NULL,NULL,NULL),
(32,173,2,2,14,103,2,17,164,NULL,NULL,NULL),
(33,173,5,2,14,104,2,17,165,NULL,NULL,NULL),
(34,173,7,2,14,105,2,17,166,NULL,NULL,NULL),
(35,173,8,2,14,106,2,17,167,NULL,NULL,NULL),
(36,173,9,2,14,107,2,17,168,NULL,NULL,NULL),
(37,173,10,2,14,108,2,17,169,NULL,NULL,NULL),
(38,173,11,2,14,109,2,17,170,NULL,NULL,NULL),
(39,173,12,2,14,110,2,17,171,NULL,NULL,NULL),
(40,173,13,2,14,111,2,17,172,NULL,NULL,NULL),
(41,173,14,2,14,112,2,17,173,NULL,NULL,NULL),
(42,173,16,2,14,113,2,17,174,NULL,NULL,NULL),
(43,173,15,2,14,114,2,17,175,NULL,NULL,NULL),
(44,173,6,2,14,207,2,17,162,NULL,NULL,NULL),
(45,170,6,3,27,115,NULL,NULL,NULL,3,31,176),
(46,170,1,3,27,116,NULL,NULL,NULL,3,31,177),
(47,170,2,3,27,117,NULL,NULL,NULL,3,31,178),
(48,170,3,3,27,118,NULL,NULL,NULL,3,31,179),
(49,170,4,3,27,119,NULL,NULL,NULL,3,31,180),
(50,170,5,3,27,120,NULL,NULL,NULL,3,31,181),
(51,170,7,3,27,121,NULL,NULL,NULL,3,31,182),
(52,170,8,3,27,122,NULL,NULL,NULL,3,31,183),
(53,170,9,3,27,123,NULL,NULL,NULL,3,31,184),
(54,170,11,3,27,124,NULL,NULL,NULL,3,31,185),
(55,170,12,3,27,125,NULL,NULL,NULL,3,31,186),
(56,170,13,3,27,126,NULL,NULL,NULL,3,31,187),
(57,170,14,3,27,127,NULL,NULL,NULL,3,31,188),
(58,170,16,3,27,128,NULL,NULL,NULL,3,31,189),
(59,170,15,3,27,129,NULL,NULL,NULL,3,31,190),
(60,170,10,3,27,28,NULL,NULL,NULL,3,31,36),
(61,172,6,3,27,115,NULL,NULL,NULL,3,31,176),
(62,172,1,3,27,116,NULL,NULL,NULL,3,31,177),
(63,172,2,3,27,117,NULL,NULL,NULL,3,31,178),
(64,172,3,3,27,118,NULL,NULL,NULL,3,31,179),
(65,172,4,3,27,119,NULL,NULL,NULL,3,31,180),
(66,172,5,3,27,120,NULL,NULL,NULL,3,31,181),
(67,172,7,3,27,121,NULL,NULL,NULL,3,31,182),
(68,172,8,3,27,122,NULL,NULL,NULL,3,31,183),
(69,172,9,3,27,123,NULL,NULL,NULL,3,31,184),
(70,172,11,3,27,124,NULL,NULL,NULL,3,31,185),
(71,172,12,3,27,125,NULL,NULL,NULL,3,31,186),
(72,172,13,3,27,126,NULL,NULL,NULL,3,31,187),
(73,172,14,3,27,127,NULL,NULL,NULL,3,31,188),
(74,172,16,3,27,128,NULL,NULL,NULL,3,31,189),
(75,172,15,3,27,129,NULL,NULL,NULL,3,31,190),
(76,172,10,3,27,28,NULL,NULL,NULL,3,31,36),
(77,164,6,6,54,130,6,60,191,NULL,NULL,NULL),
(78,164,1,6,54,131,6,60,192,NULL,NULL,NULL),
(79,164,2,6,54,132,6,60,193,NULL,NULL,NULL),
(80,164,3,6,54,133,6,60,194,NULL,NULL,NULL),
(81,164,4,6,54,134,6,60,195,NULL,NULL,NULL),
(82,164,5,6,54,135,6,60,196,NULL,NULL,NULL),
(83,164,7,6,54,136,6,60,197,NULL,NULL,NULL),
(84,164,8,6,54,137,6,60,198,NULL,NULL,NULL),
(85,164,9,6,54,138,6,60,199,NULL,NULL,NULL),
(86,164,10,6,54,139,6,60,200,NULL,NULL,NULL),
(87,164,11,6,54,140,6,60,201,NULL,NULL,NULL),
(88,164,12,6,54,141,6,60,202,NULL,NULL,NULL),
(89,164,13,6,54,142,6,60,203,NULL,NULL,NULL),
(90,164,14,6,54,143,6,60,204,NULL,NULL,NULL),
(91,164,16,6,54,144,6,60,205,NULL,NULL,NULL),
(92,164,15,6,54,145,6,60,206,NULL,NULL,NULL);