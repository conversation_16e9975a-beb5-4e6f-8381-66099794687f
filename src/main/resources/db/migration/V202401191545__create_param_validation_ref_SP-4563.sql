INSERT INTO portfolio.param_validation_ref
(code, kafka_event_code, param_name, param_value, portfolio_value, param_type_code, is_not_null)
VALUES (56, 5, 'markId', null, null, 3, TRUE),
       (57, 5, 'markType', '1', '1', 2, TRUE),
       (58, 5, 'markType', '2', '2', 2, TRUE),
       (59, 5, 'personId', null, null, 3, TRUE),
       (60, 5, 'subjectId', null, null, 1, TRUE),
       (61, 5, 'subjectName', null, null, 3, TRUE),
       (62, 5, 'themeId', null, null, 1, FALSE),
       (63, 5, 'themeName', null, null, 3, FALSE),
       (64, 5, 'attestationPeriodBeginDate', null, null, 6, FALSE),
       (65, 5, 'attestationPeriodEndDate', null, null, 6, FALSE),
       (66, 5, 'attestationPeriodId', null, null, 1, FALSE),
       (67, 5, 'organizationId', null, null, 1, TRUE),
       (68, 5, 'classId', null, null, 3, TRUE),
       (69, 5, 'className', null, null, 3, TRUE),
       (70, 5, 'curatorStaffId', null, null, 1, TRUE),
       (71, 5, 'parallelId', null, null, 2, TRUE),
       (72, 5, 'parallel', null, null, 3, TRUE),
       (73, 5, 'gradeSystemType', null, null, 3, TRUE),
       (74, 5, 'gradeSystemId', '1', '1', 2, TRUE),
       (75, 5, 'gradeSystemId', '2', '2', 2, TRUE),
       (76, 5, 'gradeSystemId', '3', '3', 2, TRUE),
       (77, 5, 'gradeSystemId', '4', '4', 2, TRUE),
       (78, 5, 'gradeSystemId', '5', '5', 2, TRUE),
       (79, 5, 'gradeSystemId', '6', '6', 2, TRUE),
       (80, 5, 'value', null, null, 3, TRUE),
       (81, 5, 'fivePointValue', null, null, 7, FALSE),
       (82, 5, 'isBorderline', null, null, 5, FALSE),
       (83, 5, 'updatedAt', null, null, 6, TRUE),
       (84, 5, 'action', '1', null, 2, TRUE),
       (85, 5, 'action', '2', null, 2, TRUE),
       (86, 5, 'action', '3', null, 2, TRUE),
       (87, 5, 'comment', null, null, 3, FALSE),
       (88, 5, 'year', null, null, 3, TRUE),
       (89, 6, 'markId', null, null, 3, TRUE),
       (90, 6, 'markType', '1', '3', 2, TRUE),
       (91, 6, 'markType', '2', '4', 2, TRUE),
       (92, 6, 'personId', null, null, 3, TRUE),
       (93, 6, 'subjectId', null, null, 1, TRUE),
       (94, 6, 'subjectName', null, null, 3, TRUE),
       (95, 6, 'attestationPeriodBeginDate', null, null, 6, FALSE),
       (96, 6, 'attestationPeriodEndDate', null, null, 6, FALSE),
       (97, 6, 'attestationPeriodId', null, null, 1, FALSE),
       (98, 6, 'staffId', null, null, 1, TRUE),
       (99, 6, 'organizationId', null, null, 2, TRUE),
       (100, 6, 'classId', null, null, 2, TRUE),
       (101, 6, 'className', null, null, 3, TRUE),
       (102, 6, 'curatorStaffId', null, null, 1, FALSE),
       (103, 6, 'parallelId', null, null, 2, TRUE),
       (104, 6, 'parallel', null, null, 3, TRUE),
       (105, 6, 'gradeSystemType', null, null, 3, TRUE),
       (106, 6, 'gradeSystemId', '1', '1', 2, TRUE),
       (107, 6, 'gradeSystemId', '2', '2', 2, TRUE),
       (108, 6, 'gradeSystemId', '3', '3', 2, TRUE),
       (109, 6, 'gradeSystemId', '4', '4', 2, TRUE),
       (110, 6, 'gradeSystemId', '5', '5', 2, TRUE),
       (111, 6, 'gradeSystemId', '6', '6', 2, TRUE),
       (112, 6, 'value', null, null, 3, TRUE),
       (113, 6, 'fivePointValue', null, null, 2, TRUE),
       (114, 6, 'updatedAt', null, null, 6, TRUE),
       (115, 6, 'action', '1', null, 2, TRUE),
       (116, 6, 'action', '2', null, 2, TRUE),
       (117, 6, 'action', '3', null, 2, TRUE),
       (118, 6, 'comment', null, null, 3, FALSE),
       (119, 6, 'year', null, null, 3, TRUE),
       (120, 7, 'staffId', null, null, 2, TRUE),
       (121, 7, 'organizationId', null, null, 2, TRUE),
       (122, 7, 'classId', null, null, 3, TRUE),
       (123, 7, 'className', null, null, 3, TRUE),
       (124, 7, 'parallel', null, null, 3, TRUE),
       (125, 7, 'subjectId', null, null, 1, TRUE),
       (126, 7, 'subjectName', null, null, 3, TRUE),
       (127, 7, 'lesson.id', null, null, 2, TRUE),
       (128, 7, 'lesson.name', null, null, 3, TRUE),
       (129, 7, 'lesson.themeName', null, null, 3, TRUE),
       (130, 7, 'lessonThemeId', null, null, 2, TRUE),
       (131, 7, 'lesson.startAt', null, null, 6, TRUE),
       (132, 7, 'attestationPeriodId', null, null, 2, TRUE),
       (133, 8, 'staffId', null, null, 2, TRUE),
       (134, 8, 'organizationId', null, null, 2, TRUE),
       (135, 8, 'classId', null, null, 3, TRUE),
       (136, 8, 'className', null, null, 3, TRUE),
       (137, 8, 'parallel', null, null, 3, TRUE),
       (138, 8, 'subjectId', null, null, 1, TRUE),
       (139, 8, 'subjectName', null, null, 3, TRUE),
       (140, 8, 'lesson.id', null, null, 2, TRUE),
       (141, 8, 'lesson.name', null, null, 3, TRUE),
       (142, 8, 'lesson.themeName', null, null, 3, TRUE),
       (143, 8, 'lesson.themeId', null, null, 2, TRUE),
       (144, 8, 'lesson.startAt', null, null, 6, TRUE),
       (145, 8, 'personId', null, null, 3, TRUE),
       (146, 8, 'reason', '1', '1', 2, TRUE),
       (147, 8, 'reason', '2', '2', 2, TRUE),
       (148, 8, 'reason', '3', '3', 2, TRUE),
       (149, 8, 'reason', '4', '4', 2, TRUE),
       (150, 8, 'periodFrom', null, null, 4, TRUE),
       (151, 8, 'periodTo', null, null, 4, TRUE),
       (152, 8, 'action', null, null, 2, TRUE);