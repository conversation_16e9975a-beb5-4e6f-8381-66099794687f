CREATE OR REPLACE FUNCTION portfolio.gather_yearly_olympiads()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    insert into portfolio.yearly_olympiads(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id AS person_id,
               COUNT(e.id) AS count_not_rewarded,
               COUNT(CASE WHEN r.reward_type_code IN (1, 2) THEN 1 END) AS count_rewarded,
               '2024-2025'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.date > '2024-09-01'
            AND r.is_delete = false
            AND r.source_code not in (10, 11)
            AND r.reward_type_code IN (1, 2)
        WHERE e.is_delete = false
          AND e.start_date > '2024-09-01'
          AND e.type_code = 8
          AND e.source_code not in (10, 11)
        GROUP BY e.person_id);

    update portfolio.yearly_result_completion
    set is_olympiad_completed = true
    where "year" = '2024-2025';

    return true;
END;
$function$
;


