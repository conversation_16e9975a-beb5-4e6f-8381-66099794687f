CREATE OR REPLACE FUNCTION portfolio.gather_yearly_competitions()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_competitions(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id AS person_id,
               COUNT(e.id) AS count_not_rewarded,
               COUNT(CASE
                         WHEN (r.reward_type_code IN
                               (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21,  28,
                                29, 30, 31, 32, 34, 35, 36, 37, 38))
                              OR (sr.type_code IN (27, 28, 29, 30, 31))
                              THEN 1
                    END) AS count_rewarded,
               '2024-2025'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.is_delete = false
            AND (r.date >= '2024-09-01' OR e.start_date >= '2024-09-01')
            AND r.reward_type_code IN
                (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21,  28,
                 29, 30, 31, 32, 34, 35, 36, 37, 38)
                 LEFT JOIN portfolio.sport_reward sr ON e.id = CAST(sr.entity_id as bigint)
            AND sr.is_delete = false
            AND (sr.date >= '2024-09-01' OR e.start_date >= '2024-09-01')
            AND sr.type_code IN (27, 28, 29, 30, 31)
        WHERE e.is_delete = false
          AND e.start_date >= '2024-09-01'
          AND e.type_code IN (15, 16, 28, 29, 30, 44, 55, 56, 57, 58, 59, 72)
        GROUP BY e.person_id
    );

    UPDATE portfolio.yearly_result_completion
    SET is_competition_completed = true
    WHERE "year" = '2024-2025';

    RETURN true;
END;
$function$
;