delete
from portfolio.administration_setting
where id in (SELECT id
             FROM portfolio.administration_setting
             WHERE ctid IN
                   (SELECT ctid
                    FROM (SELECT ctid,
                                 ROW_NUMBER() OVER
                                     (partition BY section_code, learner_category_code ORDER BY ctid) AS rnum
                          FROM portfolio.administration_setting) t
                    WHERE t.rnum > 1));