CREATE OR REPLACE FUNCTION portfolio.update_aggregated_success_subject()
    RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             success_subject, success_subject_mark, not_one, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           subject,
           mark,
           not_one,
           '2024-2025'
    FROM portfolio.yearly_success_subject
    ON CONFLICT (person_id, "year") DO UPDATE
        SET success_subject      = EXCLUDED.success_subject,
            success_subject_mark = EXCLUDED.success_subject_mark;
    RETURN true;
END;
$$ LANGUAGE plpgsql;



CREATE OR REPLACE FUNCTION portfolio.gather_yearly_olympiads()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    insert into portfolio.yearly_olympiads(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id                               AS person_id,
               COUNT(CASE
                         WHEN (r.entity_id IS NULL AND sr.entity_id IS NULL OR
                               (r.reward_type_code NOT IN
                                (1, 2, 3))
                             OR (sr.type_code NOT IN
                                 (1, 2, 3))) THEN 1 END) AS count_not_rewarded,
               COUNT(CASE
                         WHEN (r.reward_type_code IN
                               (1, 2, 3))
                             OR (sr.type_code IN
                                 (1, 2, 3)) THEN 1 END)  AS count_rewarded,
               '2024-2025'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.date > '2024-09-01'
            AND r.is_delete = false
            AND r.source_code not in (10, 11)
            AND r.reward_type_code IN (1, 2, 3)
                 LEFT JOIN portfolio.sport_reward sr ON e.id = CAST(sr.entity_id as bigint)
            AND sr.date > '2024-09-01'
            AND sr.is_delete = false
            AND sr.source_code not in (10, 11)
            AND sr.type_code IN (1, 2, 3)
        WHERE e.is_delete = false
          AND e.start_date > '2024-09-01'
          AND e.type_code = 8
          AND e.source_code not in (10, 11)
        GROUP BY e.person_id);
    update portfolio.yearly_result_completion set is_olympiad_completed = true where "year" = '2024-2025';
    return true;
END;
$function$
;


CREATE OR REPLACE FUNCTION portfolio.update_aggregated_olympiad()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, olympiad_not_rewarded,
                                             olympiad_rewarded, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.count_not_rewarded,
            NEW.count_rewarded, '2024-2025')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET olympiad_not_rewarded = EXCLUDED.olympiad_not_rewarded,
            olympiad_rewarded     = EXCLUDED.olympiad_rewarded;
    RETURN NEW;
END;
$function$
;



CREATE OR REPLACE FUNCTION portfolio.gather_yearly_competitions()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_competitions(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               e.person_id AS person_id,
               COUNT(CASE
                         WHEN (r.entity_id IS NULL AND sr.entity_id IS NULL)
                              THEN 1
                         WHEN (r.entity_id IS NOT NULL AND r.reward_type_code NOT IN
                               (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                                29, 30, 31, 32, 34, 35, 36))
                              AND (sr.entity_id IS NULL OR sr.type_code NOT IN (27, 28, 29, 30, 31))
                              THEN 1
                         WHEN (sr.entity_id IS NOT NULL AND sr.type_code NOT IN (27, 28, 29, 30, 31))
                              AND (r.entity_id IS NULL OR r.reward_type_code NOT IN
                                   (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                                    29, 30, 31, 32, 34, 35, 36))
                              THEN 1
                    END) AS count_not_rewarded,
               COUNT(CASE
                         WHEN (r.reward_type_code IN
                               (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                                29, 30, 31, 32, 34, 35, 36))
                              OR (sr.type_code IN (27, 28, 29, 30, 31))
                              THEN 1
                    END) AS count_rewarded,
               '2024-2025'
        FROM portfolio.event e
                 LEFT JOIN portfolio.reward r ON e.id = CAST(r.entity_id as bigint)
            AND r.is_delete = false
            AND (r.date >= '2024-09-01' OR e.start_date >= '2024-09-01')
            AND r.reward_type_code IN
                (5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23, 24, 28,
                 29, 30, 31, 32, 34, 35, 36)
                 LEFT JOIN portfolio.sport_reward sr ON e.id = CAST(sr.entity_id as bigint)
            AND sr.is_delete = false
            AND (sr.date >= '2024-09-01' OR e.start_date >= '2024-09-01')
            AND sr.type_code IN (27, 28, 29, 30, 31)
        WHERE e.is_delete = false
          AND e.start_date >= '2024-09-01'
          AND e.type_code IN (15, 16, 28, 29, 30, 44, 55, 56, 57, 58, 59, 72)
        GROUP BY e.person_id
    );

    UPDATE portfolio.yearly_result_completion
    SET is_competition_completed = true
    WHERE "year" = '2024-2025';

    RETURN true;
END;
$function$
;


CREATE OR REPLACE FUNCTION portfolio.update_aggregated_competition()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, competitions_not_rewarded,
                                             competitions_rewarded, "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.count_not_rewarded,
            NEW.count_rewarded, '2024-2025')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET competitions_not_rewarded = EXCLUDED.competitions_not_rewarded,
            competitions_rewarded     = EXCLUDED.competitions_rewarded;
    RETURN NEW;
END;
$function$
;



CREATE OR REPLACE FUNCTION portfolio.gather_yearly_additional_education()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    insert into portfolio.yearly_additional_education(
        SELECT nextval('hibernate_sequence'),
               now(),
               now(),
               person_id,
               count(*) as additional_education_count,
               '2024-2025'
        FROM portfolio.employment e
        WHERE e.start_date >= '2024-09-01'
            AND e.is_delete = false
        GROUP BY e.person_id);
    update portfolio.yearly_result_completion set is_achievements_completed = true where "year" = '2024-2025';
    return true;
END;
$function$
;

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_additional_education()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, additional_education_count,
                                             "year")
    VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.additional_education_count, '2024-2025')
    ON CONFLICT (person_id, "year") DO UPDATE
        SET additional_education_count = EXCLUDED.additional_education_count;
    RETURN NEW;
END;
$function$
;
