create table portfolio.yearly_additional_education
(
    id                         bigint primary key,
    creation_date              timestamp not null default now(),
    edit_date                  timestamp not null default now(),
    person_id                  varchar   not null,
    additional_education_count integer,
    year                       varchar
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_additional_education()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.person_id IN (SELECT person_id FROM portfolio.yearly_aggregated) THEN
        UPDATE portfolio.yearly_aggregated
        SET additional_education_count = NEW.additional_education_count
        WHERE person_id = NEW.person_id;
    ELSE
        INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id, additional_education_count, "year")
        VALUES (nextval('hibernate_sequence'), now(), now(), NEW.person_id, NEW.additional_education_count, '2023-2024');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER additional_education_trigger
    AFTER INSERT
    ON portfolio.yearly_additional_education
    FOR EACH ROW
EXECUTE FUNCTION portfolio.update_aggregated_additional_education();