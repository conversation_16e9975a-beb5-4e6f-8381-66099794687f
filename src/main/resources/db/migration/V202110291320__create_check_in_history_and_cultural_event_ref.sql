create table portfolio.cultural_event_ref
(
    code  smallint
        constraint cultural_event_ref_pk
            primary key,
    value varchar(100)
);

create table portfolio.check_in_history
(
    id                  bigint
        constraint check_in_history_pk
            primary key,
    person_id           varchar(50) not null,
    description         text,
    organizer           varchar,
    linked_object_ids   varchar,
    event               text,
    museum_code           bigint
        constraint check_in_history_museum_ref_code_fk
            references portfolio.museum_ref,
    theatre_code          bigint
        constraint check_in_history_theatre_ref_code_fk
            references portfolio.theatre_ref,
    cinema_code           bigint
        constraint check_in_history_cinema_ref_code_fk
            references portfolio.cinema_ref,
    category_code       smallint    not null
        constraint check_in_history_section_ref_code_fk
            references portfolio.section_ref,
    creation_date       timestamp   not null,
    type_code           smallint
        constraint check_in_history_section_ref_code_fk_2
            references portfolio.section_ref,
    format_code         smallint    not null
        constraint check_in_history_olympiad_format_ref_code_fk
            references portfolio.olympiad_format_ref,
    level_code          smallint
        constraint check_in_history_olympiad_type_ref_code_fk
            references portfolio.olympiad_type_ref,
    cultural_event_code smallint,
    date                date        not null,
    data_kind           smallint,
    source_code         smallint    not null
);

alter table portfolio.check_in_history
    add constraint check_in_history_cultural_event_ref_code_fk
        foreign key (cultural_event_code) references portfolio.cultural_event_ref;

alter table portfolio.check_in_history
    add is_delete boolean default false not null;

INSERT INTO "portfolio"."cultural_event_ref" ("code", "value")
VALUES (1, 'Посещение музея');
INSERT INTO "portfolio"."cultural_event_ref" ("code", "value")
VALUES (2, 'Посещение театра');
INSERT INTO "portfolio"."cultural_event_ref" ("code", "value")
VALUES (3, 'Посещение кинотеатра');
