create table if not exists portfolio.yearly_lesson
(
    id            bigint primary key default nextval('portfolio.hibernate_sequence'),
    creation_date timestamp not null default now(),
    edit_date     timestamp not null default now(),
    person_id     varchar   not null,
    passed_lessons_count    integer   not null,
    year          varchar            default '2024-2025'
);

CREATE OR REPLACE FUNCTION portfolio.update_aggregated_lesson()
    RETURNS BOOLEAN AS
$$
BEGIN
    INSERT INTO portfolio.yearly_aggregated (id, creation_date, edit_date, person_id,
                                             passed_lesson_count, "year")
    SELECT nextval('hibernate_sequence'),
           now(),
           now(),
           person_id,
           passed_lessons_count,
           '2024-2025'
    FROM portfolio.yearly_lesson
    ON CONFLICT (person_id, "year") DO UPDATE
        SET passed_lesson_count      = EXCLUDED.passed_lesson_count;
    RETURN true;
END;
$$ LANGUAGE plpgsql;