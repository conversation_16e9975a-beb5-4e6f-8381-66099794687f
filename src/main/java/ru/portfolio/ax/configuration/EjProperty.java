package ru.portfolio.ax.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Конфигурация для API ЭЖ (Электронный журнал)
 */
@Data
@Component
@ConfigurationProperties("ej.api")
public class EjProperty {

    /**
     * Базовый URL API ЭЖ
     */
    private String url;

    /**
     * Endpoint для получения общего среднего балла
     */
    private String rankClassEndpoint;

    /**
     * Endpoint для получения рейтинга по предметам
     */
    private String rankSubjectsEndpoint;

    /**
     * Подсистема для заголовка x-mes-subsystem
     */
    private String subsystem;
}
