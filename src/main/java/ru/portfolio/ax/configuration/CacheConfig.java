package ru.portfolio.ax.configuration;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
@RequiredArgsConstructor
public class CacheConfig {

    /**
     * Имя кеша для данных рейтинга класса из API ЭЖ
     */
    public static final String EJ_RANK_CLASS_CACHE = "ejRankClass";

    /**
     * Имя кеша для данных рейтинга по предметам из API ЭЖ
     */
    public static final String EJ_RANK_SUBJECTS_CACHE = "ejRankSubjects";

    @Value("${nsi.cache.duration.minutes}")
    private Integer nsiCacheDurationMinutes;

    @Value("${nsi.cache.maxItems}")
    private Integer nsiCacheMaxItems;

    @Bean
    @Primary
    public CacheManager defaultCacheManager() {
        return new ConcurrentMapCacheManager();
    }

    @Bean("nsiCacheManager")
    public CacheManager nsiCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("getOrgNameFromNsiService");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .recordStats()
                .expireAfterWrite(nsiCacheDurationMinutes, TimeUnit.MINUTES)
                .maximumSize(nsiCacheMaxItems));
        return cacheManager;
    }

    @Bean("ejCacheManager")
    public CacheManager ejCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager(EJ_RANK_CLASS_CACHE, EJ_RANK_SUBJECTS_CACHE);
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .recordStats()
                .expireAfterWrite(1, TimeUnit.HOURS) // TTL 1 час для API ЭЖ
                .maximumSize(1000)); // Максимум 1000 записей
        return cacheManager;
    }
}
